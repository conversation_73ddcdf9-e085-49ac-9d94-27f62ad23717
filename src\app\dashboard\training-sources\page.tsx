"use client";
import { useState } from "react";
import { SourcesPage } from "./components/sources-page";

const TrainingAndSources = () => {
  // Since we have mock data in the SourcesPage component (TOTAL_SOURCES = 68),
  // we'll just directly show the SourcesPage instead of the empty state
  const [showAddDialog, setShowAddDialog] = useState(false);

  const handleAddNewClick = () => {
    setShowAddDialog(true);
  };

  return (
    <SourcesPage
      showAddDialog={showAddDialog}
      setShowAddDialog={setShowAddDialog}
      onAddNewClick={handleAddNewClick}
    />
  );
};

export default TrainingAndSources;
