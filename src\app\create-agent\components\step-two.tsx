"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useCreateBusinessMutation } from "@/store/features/businessApi";
import useAgentStore from "@/store/features/useAgentStore";
import { useToast } from "@/hooks/use-toast";
import PhoneInput, { isPossiblePhoneNumber } from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { E164Number } from "libphonenumber-js";

// Define interface for StepTwo props
// Define a type for the business creation result
interface BusinessCreationResult {
  id: string;
  business_name: string;
  business_phone: string;
  type_of_business: string;
  additional_context?: string;
  created_at?: string;
}

interface StepTwoProps {
  onNextStep: (result?: BusinessCreationResult) => void;
}

const StepTwo: React.FC<StepTwoProps> = ({ onNextStep }) => {
  // Form state
  const [systemContext, setSystemContext] = useState("");
  const [businessName, setBusinessName] = useState("");
  const [businessType, setBusinessType] = useState("");
  const [phoneNumber, setPhoneNumber] = useState<E164Number | undefined>();
  const [isPhoneValid, setIsPhoneValid] = useState(true);

  // RTK Query mutation hook
  const [createBusiness, { isLoading, error: apiError }] =
    useCreateBusinessMutation();

  // Get voice agent ID from store
  const voiceAgentId = useAgentStore((state) => state.voiceAgentId);
  const agentName = useAgentStore((state) => state.agentName);

  // Toast notification
  const { toast } = useToast();

  // Validate phone number when it changes
  useEffect(() => {
    if (phoneNumber) {
      setIsPhoneValid(isPossiblePhoneNumber(phoneNumber));
    } else {
      setIsPhoneValid(true); // Empty is considered valid until submission
    }
  }, [phoneNumber]);

  // Check if we have a voice agent ID
  useEffect(() => {
    if (!voiceAgentId) {
      toast({
        title: "Error",
        description:
          "No voice agent ID found. Please go back and create an agent first.",
        variant: "destructive",
      });
    } else {
      console.log("Using voice agent ID:", voiceAgentId);
    }
  }, [voiceAgentId, toast]);

  // Display API errors as toast
  useEffect(() => {
    if (apiError) {
      toast({
        title: "Error",
        description: "Failed to create business. Please try again.",
        variant: "destructive",
      });
    }
  }, [apiError, toast]);

  // Form submission handler
  const handleNext = async () => {
    // Validate form
    if (!businessName || !businessType || !phoneNumber || !isPhoneValid) {
      toast({
        title: "Validation Error",
        description: "Please fill all fields correctly before continuing",
        variant: "destructive",
      });
      return;
    }

    // Check if we have a voice agent ID
    if (!voiceAgentId) {
      toast({
        title: "Error",
        description:
          "No voice agent ID found. Please go back and create an agent first.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Prepare business data
      const businessData = {
        business_name: businessName,
        business_phone: phoneNumber,
        type_of_business: businessType,
        additional_context: systemContext,
      };

      // Call RTK Query mutation to create business
      const result = await createBusiness({
        voiceAgentId,
        data: businessData,
      }).unwrap();

      // Show success toast
      toast({
        title: "Success",
        description: "Business created successfully!",
      });

      // Move to next step
      if (onNextStep) {
        onNextStep(result);
      }
    } catch (err) {
      console.error("Error creating business:", err);
      // Error is handled by the useEffect above
    }
  };

  return (
    <Card className="w-full border-none shadow-none">
      <CardHeader>
        <CardTitle className="text-center text-3xl font-bold">
          Write additional context for {agentName || "Your Agent"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-center text-gray-600 mb-8">
          More data to enhance the voice chat bot which help to answered
          multiple types of queries
        </p>
        <div className="space-y-4 max-w-2xl mx-auto">
          {/* Business Name */}
          <div className="relative">
            <Input
              placeholder="Business_name"
              className="w-full h-12 p-4 pl-4 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#E74F13] focus:ring-opacity-20 focus:border-[#E74F13]"
              value={businessName}
              onChange={(e) => setBusinessName(e.target.value)}
              required
            />
            {businessName && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
              </div>
            )}
          </div>

          {/* Type of Business */}
          <div className="relative">
            <Input
              placeholder="Type of business"
              className="w-full h-12 p-4 pl-4 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#E74F13] focus:ring-opacity-20 focus:border-[#E74F13]"
              value={businessType}
              onChange={(e) => setBusinessType(e.target.value)}
              required
            />
            {businessType && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
              </div>
            )}
          </div>

          {/* Phone Number with react-phone-number-input */}
          <div className="relative">
            {/* Custom styling for PhoneInput to match other inputs */}
            <style jsx global>{`
              .PhoneInput {
                height: 48px;
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                display: flex;
                align-items: center;
                padding: 0 12px;
              }

              .PhoneInput:focus-within {
                outline: none;
                ring: 2px;
                ring-color: rgba(231, 79, 19, 0.2);
                border-color: #e74f13;
              }

              .PhoneInputCountry {
                margin-right: 8px;
                min-width: 40px;
              }

              .PhoneInputInput {
                border: none;
                flex: 1;
                height: 46px;
                font-size: 16px;
                padding: 0;
                outline: none;
                background: transparent;
              }

              .PhoneInputCountryIcon {
                width: 24px;
                height: 16px;
              }

              .PhoneInputCountrySelectArrow {
                margin-left: 4px;
                width: 8px;
                height: 8px;
                border-width: 1px 1px 0 0;
              }
            `}</style>

            <PhoneInput
              placeholder="Business phone"
              value={phoneNumber}
              onChange={setPhoneNumber}
              defaultCountry="US"
              international={true}
              countryCallingCodeEditable={true}
              initialValueFormat="national"
              displayInitialValueAsLocalNumber={true}
              error={
                phoneNumber
                  ? isPhoneValid
                    ? undefined
                    : "Invalid phone number"
                  : undefined
              }
              required
            />

            {phoneNumber && isPhoneValid && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
              </div>
            )}

            {phoneNumber && !isPhoneValid && (
              <div className="mt-1 text-sm text-red-500">
                Please enter a valid phone number
              </div>
            )}
          </div>

          {/* Write Here Textarea */}
          <textarea
            placeholder="Write here additional context about your business"
            className="w-full h-48 p-4 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#E74F13] focus:ring-opacity-20 focus:border-[#E74F13] resize-none"
            value={systemContext}
            onChange={(e) => setSystemContext(e.target.value)}
          />

          {/* Continue Button */}
          <Button
            className="w-full bg-[#E74F13] hover:bg-[#E74F13]/90 h-12 rounded-lg transition-colors duration-200"
            onClick={handleNext}
            disabled={
              isLoading || (phoneNumber && !isPhoneValid) || !voiceAgentId
            }
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </div>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default StepTwo;
