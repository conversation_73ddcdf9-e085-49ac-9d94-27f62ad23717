"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, YAxis } from "recharts";

import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function CustomerHabitsChart() {
  const data = [
    { day: "Monday", online: 25000, offline: 20000 },
    { day: "Tuesday", online: 12000, offline: 7500 },
    { day: "Wednesday", online: 16000, offline: 3500 },
    { day: "Thursday", online: 11000, offline: 3000 },
    { day: "Friday", online: 8000, offline: 7000 },
    { day: "Saturday", online: 12000, offline: 9000 },
    { day: "Sunday", online: 15000, offline: 7000 },
  ];

  return (
    <div className="w-full  rounded-lg border bg-white p-6 shadow-sm">
      <div className="mb-0 flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">
            Customer Habits
          </h2>
          <p className="text-sm text-gray-500">Track Your Customer Habits</p>
        </div>
        <div className="relative">
          <Select defaultValue="thisYear">
            <SelectTrigger className="w-32 h-10  rounded-full bg-[#E8E8E8]">
              <SelectValue placeholder="Time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="thisYear">This Year</SelectItem>
              <SelectItem value="lastYear">Last Year</SelectItem>
              <SelectItem value="allTime">All Time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex items-center gap-6 mb-4">
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 rounded-full bg-[#ff6b35]"></div>
          <span className="text-sm text-gray-600">Online Sales</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 rounded-full bg-[#ff9a7a]"></div>
          <span className="text-sm text-gray-600">Offline Sales</span>
        </div>
      </div>

      <ChartContainer
        config={{
          online: {
            label: "Online Sales",
            color: "#ff6b35",
          },
          offline: {
            label: "Offline Sales",
            color: "#ff9a7a",
          },
        }}
        className="h-[264px] w-[100%]"
      >
        <BarChart
          accessibilityLayer
          data={data}
          margin={{
            top: 5,
            right: 10,
            left: -20,
            bottom: 5,
          }}
          barGap={0}
        >
          <CartesianGrid
            horizontal={true}
            vertical={false}
            strokeDasharray="3 3"
            opacity={0.6}
          />
          <XAxis
            dataKey="day"
            axisLine={false}
            tickLine={false}
            tickMargin={10}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tickMargin={10}
            tickFormatter={(value) => (value === 0 ? "0" : `${value / 1000}k`)}
            domain={[0, 25000]}
            ticks={[0, 5000, 10000, 15000, 20000, 25000]}
          />
          <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
          <Bar
            dataKey="online"
            radius={[20, 20, 20, 20]}
            barSize={40}
            fill="#E74F13"
          />
          <Bar
            dataKey="offline"
            radius={[20, 20, 20, 20]}
            barSize={40}
            fill="#ff6b35"
          />
        </BarChart>
      </ChartContainer>
    </div>
  );
}
