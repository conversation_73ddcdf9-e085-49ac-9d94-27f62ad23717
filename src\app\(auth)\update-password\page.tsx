"use client";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useUpdatePasswordMutation } from "@/store/features/authApi";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import AuthLayout from "../components/auth-layout";

interface ApiError {
  message?: string;
  data?: {
    message?: string;
  };
}


export default function SetNewPasswordForm() {
  const [passwords, setPasswords] = useState({
    password: "",
    confirmPassword: "",
  });
  const [error, setError] = useState("");
  const [updatePassword, { isLoading }] = useUpdatePasswordMutation();
  const { toast } = useToast();
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswords((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (passwords.password !== passwords.confirmPassword) {
      setError("Passwords do not match");
      return;
    }
    try {
      await updatePassword(passwords.password).unwrap();
      toast({
        description: "Password updated successfully",
      });
      router.push("/signin");
    } catch (error: unknown) {
      const apiError = error as ApiError;
      toast({
        description:
          apiError.message ||
          apiError.data?.message ||
          "Failed to update password",
        variant: "destructive",
      });
    }
  };

  return (
    <AuthLayout heading="Set New Password" subheading="">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            placeholder="********"
            value={passwords.password}
            onChange={handleChange}
            required
            className="h-12 border-gray-200 focus:border-[#E84B1C] focus:ring-2 focus:ring-[#E84B1C] focus:ring-opacity-20"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <Input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            placeholder="********"
            value={passwords.confirmPassword}
            onChange={handleChange}
            required
            className="h-12 border-gray-200 focus:border-[#E84B1C] focus:ring-2 focus:ring-[#E84B1C] focus:ring-opacity-20"
          />
          {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-[#E84B1C] hover:bg-[#E84B1C]/90 text-white mt-6"
          disabled={isLoading}
        >
          Reset Password
        </Button>
      </form>
    </AuthLayout>
  );
}
