"use client";
import React, { useState, useRef } from "react";
import { X, FileText, Upload, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import useAgentStore from "@/store/features/useAgentStore";


interface FileUploadTabProps {
  title: string;
  description: string;
  acceptTypes: string;
  uploadButtonText?: string;
  onUploadSuccess?: () => void;
}

export function FileUploadTab({
  title,
  description,
  acceptTypes,
  uploadButtonText = "Upload Files",
  onUploadSuccess,
}: FileUploadTabProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Get voice agent ID from store
  const voiceAgentId = useAgentStore((state) => state.voiceAgentId);

  

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);

      // Filter for supported file types
      const supportedFiles = files.filter((file) => {
        const ext = file.name.split(".").pop()?.toLowerCase();
        return [".csv", ".xls", ".xlsx"].some((supportedExt) =>
          supportedExt.includes(ext || "")
        );
      });

      if (supportedFiles.length < files.length) {
        toast.warning(
          "Some files were skipped because they are not supported."
        );
      }

      setSelectedFiles((prev) => [...prev, ...supportedFiles]);
    }
  };

  const handleClearFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleClearAllFiles = () => {
    setSelectedFiles([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUploadClick = async () => {
    if (!voiceAgentId) {
      toast.error("No voice agent ID found. Please create an agent first.");
      return;
    }

    if (selectedFiles.length === 0) {
      toast.error("Please select at least one file to upload.");
      return;
    }

    setIsUploading(true);

    try {
    

      toast.success("Files uploaded successfully for training!");
      handleClearAllFiles();

      if (onUploadSuccess) {
        onUploadSuccess();
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload files. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  };

  return (
    <div>
      <h3 className="text-lg font-medium">{title}</h3>
      <p className="text-sm text-gray-500 mt-1 mb-4">{description}</p>

      <div className="mb-6">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
          <input
            ref={fileInputRef}
            type="file"
            accept={acceptTypes}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />
          <button
            onClick={handleAttachClick}
            className="w-full h-full flex flex-col items-center justify-center cursor-pointer"
          >
            <Upload className="h-10 w-10 text-gray-400 mb-2" />
            <span className="font-medium">Click to upload CSV files</span>
            <span className="text-sm text-gray-500">
              or drag and drop files here
            </span>
            <span className="text-xs text-gray-400 mt-2">
              Supported formats: CSV, XLS, XLSX (Max size: 25MB)
            </span>
          </button>
        </div>

        {/* Selected files list */}
        {selectedFiles.length > 0 && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-md font-medium">
                Selected Files ({selectedFiles.length})
              </h4>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAllFiles}
                className="text-xs text-red-500"
              >
                Remove All
              </Button>
            </div>

            <div className="space-y-2 max-h-60 overflow-y-auto pr-2 rounded-lg border border-gray-100 p-2 bg-gray-50">
              {selectedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg border border-blue-200 bg-white shadow-sm"
                >
                  <div className="flex items-center gap-3 overflow-hidden">
                    <FileText className="shrink-0 text-blue-600" />
                    <div className="truncate text-sm">
                      <p className="font-medium truncate">{file.name}</p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-full text-gray-400 hover:text-red-500"
                    onClick={() => handleClearFile(index)}
                  >
                    <X size={16} />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        <Button
          onClick={handleUploadClick}
          disabled={selectedFiles.length === 0 || isUploading}
          className="w-full mt-4 bg-orange-500 hover:bg-orange-600 text-white"
        >
          {isUploading ? (
            <div className="flex items-center">
              <Loader2 className="animate-spin mr-2 h-4 w-4" />
              Uploading...
            </div>
          ) : (
            uploadButtonText
          )}
        </Button>
      </div>
    </div>
  );
}
