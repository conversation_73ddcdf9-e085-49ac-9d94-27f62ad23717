import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { useAuthStore } from "@/store/features/useAuthStore";

// Types
interface VoiceAgent {
  id: string;
  name: string;
  systemContext?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateVoiceAgentRequest {
  name: string;
  systemContext?: string;
  private_settings?: {
    model?: {
      voice?: string;
    };
  };
}

interface CreateVoiceAgentResponse {
  id: string;
  name: string;
  systemContext?: string;
  private_settings?: {
    model?: {
      voice?: string;
    };
  };
}

interface RootState {
  auth?: {
    token: string | null;
  };
}

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Function to get token from Zustand store
const getZustandToken = (): string | null => {
  return useAuthStore.getState().token;
};

export const voiceAgentApi = createApi({
  reducerPath: "voiceAgentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: async (headers, { getState }) => {
      // First try to get token from Zustand store
      let token = getZustandToken();

      // If no token in Zustand, try Redux state
      if (!token) {
        token = (getState() as RootState).auth?.token || null;
      }

      // If still no token, try Supabase session as last resort
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      headers.set("Content-Type", "application/json");

      return headers;
    },
  }),
  endpoints: (builder) => ({
    // Create a new voice agent
    createVoiceAgent: builder.mutation<
      CreateVoiceAgentResponse,
      CreateVoiceAgentRequest
    >({
      query: (data) => ({
        url: "/v1/voice-agents",
        method: "POST",
        body: {
          name: data.name,
          private_settings: data.private_settings,
        },
      }),
    }),

    // Get all voice agents for the user
    getVoiceAgents: builder.query<VoiceAgent[], void>({
      query: () => "/v1/voice-agents",
    }),

    // Get a specific voice agent by ID
    getVoiceAgent: builder.query<VoiceAgent, string>({
      query: (id) => `/v1/voice-agents/${id}`,
    }),

    // Update a voice agent
    updateVoiceAgent: builder.mutation<
      VoiceAgent,
      { id: string; data: Partial<CreateVoiceAgentRequest> }
    >({
      query: ({ id, data }) => ({
        url: `/v1/voice-agents/${id}`,
        method: "PATCH",
        body: data,
      }),
    }),

    // Delete a voice agent
    deleteVoiceAgent: builder.mutation<void, string>({
      query: (id) => ({
        url: `/v1/voice-agents/${id}`,
        method: "DELETE",
      }),
    }),
  }),
});

export const {
  useCreateVoiceAgentMutation,
  useGetVoiceAgentsQuery,
  useGetVoiceAgentQuery,
  useUpdateVoiceAgentMutation,
  useDeleteVoiceAgentMutation,
} = voiceAgentApi;
