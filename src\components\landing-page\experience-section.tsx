import Image from "next/image";
import experience from "../../../public/images/experience.svg";

const ExperienceSection = () => {
  return (
    <section
      id="about"
      className="relative min-h-screen w-full overflow-hidden "
    >
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src={experience}
          alt="Background Dashboard"
          fill
          className="object-cover"
          priority
        />
        {/* Orange overlay for better text readability */}
        <div className="absolute inset-0 " />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-20">
        <div className="max-w-4xl">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            We bring a wealth of experience from a wide range of backgrounds
          </h2>

          <h3 className="text-2xl md:text-3xl text-white mb-8">
            A product that instantly converts your entire workforce into
            AI-enabled super employees
          </h3>

          <p className="text-white/90 text-lg mb-12 max-w-2xl">
            AI enthusiasts, and customer experience experts. With a shared
            vision of making technology more intuitive and accessible,
            we&apos;ve built a platform that transforms the way people engage
            with digital systems.
          </p>

          <div className="space-y-6">
            {/* Bullet points with custom dot style */}
            <div className="flex items-start space-x-4">
              <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0" />
              <p className="text-white">
                Create and deploy AI agents capable of handling a wide range of
                general customer queries efficiently.
              </p>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0" />
              <p className="text-white max-w-2xl">
                Automatically route more complex or unique queries to human
                agents while providing context for a smoother transition.
              </p>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0" />
              <p className="text-white">
                Facilitate appointment booking directly through AI agents,
                reducing friction in scheduling processes.
              </p>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0" />
              <div className="text-white">
                <p className="mb-2">
                  Track and analyze key performance metrics, including:
                </p>
                <ul className="list-disc list-inside pl-4 space-y-1">
                  <li>The percentage of problems resolved by AI.</li>
                  <li>
                    Detailed AI chat logs to identify areas for improvement.
                  </li>
                </ul>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0" />
              <div className="text-white">
                <p className="mb-2">Ensure AI agents are designed to:</p>
                <ul className="list-disc list-inside pl-4 space-y-1">
                  <li>
                    Fully comprehend company policies, procedures, and FAQs.
                  </li>
                  <li>
                    Identify and effectively resolve recurring issues,
                    minimizing customer frustration.
                  </li>
                  <li>
                    Adapt to changes in company policies or product updates with
                    minimal retraining.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;
