import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { supabase } from "@/lib/supabase";
import { AuthError } from "@supabase/supabase-js";
import { useAuthStore } from "@/store/features/useAuthStore";

// Types
interface User {
  id: string;
  email: string;
  username: string;
  company?: string;
  phone?: string;
  title?: string;
}

interface AuthCredentials {
  email: string;
  password: string;
}

interface AuthResponse {
  user: User;
  token: string;
  access_token?: string; // Add this for API response compatibility
  refreshToken?: string;
  refresh_token?: string;
}

interface SignUpCredentials {
  email: string;
  password: string;
  username: string;
}

interface UpdateProfileData {
  username?: string;
  phone: string;
  company: string;
  title: string;
}

interface CustomerData {
  id: string;
  username: string;
  email: string;
  company: string;
  phone: string;
  title: string;
  // Add any other fields returned by the API
}

interface RootState {
  auth?: {
    token: string | null;
    
  };
}


const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Function to get token from Zustand store
const getZustandToken = (): string | null => {
  return useAuthStore.getState().token;
};

// Fallback function to get token from Supabase session
const getSupabaseSession = async (): Promise<string | null> => {
  const {
    data: { session },
    error,
  } = await supabase.auth.getSession();
  if (error) {
    console.error("Error getting session:", error.message);
    return null;
  }
  return session?.access_token ?? null;
};

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: async (headers, { getState }) => {
      // First try to get token from Zustand store
      let token = getZustandToken();

      // If no token in Zustand, try Redux state
      if (!token) {
       token = (getState() as RootState).auth?.token || null;
      }

      // If still no token, try Supabase session as last resort
      if (!token) {
        token = await getSupabaseSession();
      }

      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      return headers;
    },
  }),
  endpoints: (builder) => ({
    signIn: builder.mutation<AuthResponse, AuthCredentials>({
      query: (credentials) => ({
        url: "/auth/login",
        method: "POST",
        body: credentials,
      }),
    }),

    signUp: builder.mutation<AuthResponse, SignUpCredentials>({
      query: (credentials) => ({
        url: "/auth/register",
        method: "POST",
        body: credentials,
      }),
    }),

    updateProfile: builder.mutation<void, UpdateProfileData>({
      query: (data) => {
        // Get token from Zustand store
        const token = getZustandToken();

        return {
          url: "/customers/update-profile",
          method: "PATCH",
          body: {
            company: data.company,
            phone: data.phone,
            title: data.title,
          },
          headers: token ? { Authorization: `Bearer ${token}` } : {},
        };
      },
    }),

    resetPassword: builder.mutation<void, string>({
      queryFn: async (email) => {
        try {
          const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/update-password`,
          });
          if (error) throw error;
          return { data: undefined };
        } catch (error) {
          return {
            error: {
              status: 400,
              data: {
                message:
                  error instanceof AuthError
                    ? error.message
                    : "An error occurred",
              },
            },
          };
        }
      },
    }),

    updatePassword: builder.mutation<void, string>({
      queryFn: async (newPassword) => {
        try {
          const { error } = await supabase.auth.updateUser({
            password: newPassword,
          });
          if (error) throw error;
          return { data: undefined };
        } catch (error) {
          return {
            error: {
              status: 400,
              data: {
                message:
                  error instanceof AuthError
                    ? error.message
                    : "An error occurred",
              },
            },
          };
        }
      },
    }),

    // Add a new endpoint to get the current user's profile
    getProfile: builder.query<User, void>({
      query: () => "/auth/me",
    }),

      getCustomerData: builder.query<CustomerData, void>({
      query: () => "/customers",
    }),
  
  }),
});

export const {
  useSignUpMutation,
  useSignInMutation,
  useUpdateProfileMutation,
  useResetPasswordMutation,
  useUpdatePasswordMutation,
  useGetProfileQuery,
  useGetCustomerDataQuery,
} = authApi;
