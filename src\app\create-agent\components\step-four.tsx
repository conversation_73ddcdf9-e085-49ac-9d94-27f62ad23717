"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Mic, Loader2, Volume2, PhoneOff, MicOff, Phone } from "lucide-react";
import useAgentStore from "@/store/features/useAgentStore";
import { useToast } from "@/hooks/use-toast";
import logo from "../../../../public/images/verbal.webp";
import agent from "../../../../public/images/agent-chat.png";
import Link from "next/link";

// Define types for our data structures
interface ChatMessage {
  text: string;
  isAi: boolean;
  timestamp: string;
  id: string;
}

interface TranscriptMessage {
  role: string;
  transcript: string;
  time: string;
}

interface CallLog {
  voice_agent_id: string;
  call_start: string;
  call_end: string | null;
  duration: string | null;
  transcript: TranscriptMessage[] | null;
  status: string;
}
type BodyValue = string | number | boolean | null | BodyObject | BodyValue[];
interface BodyObject {
  [key: string]: BodyValue;
}

interface FunctionData {
  name: string;
  data: {
    req_url: string;
    req_type: string;
    body?: Record<string, BodyValue>;
    headers: Record<string, string>;
    query?: Record<string, string>;
  };
}
interface AudioContextFallback extends Window {
  AudioContext: typeof AudioContext;
  webkitAudioContext?: typeof AudioContext;
}

interface FunctionCall {
  name: string;
  arguments: string;
  call_id?: string;
}

const StepFour = () => {
  const [isCallActive, setIsCallActive] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isUserSpeaking, setIsUserSpeaking] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const localStreamRef = useRef<MediaStream | null>(null);
  const agentName = useAgentStore((state) => state.agentName);

  // Create refs for DOM elements we need to access
  const scrollAreaRef = useRef<HTMLDivElement | null>(null);
  const audioTrackRef = useRef<MediaStreamTrack | null>(null);

  // Get voice agent ID from store
  const voiceAgentId = useAgentStore((state) => state.voiceAgentId);
  const { toast } = useToast();

  // Log agent ID for debugging
  useEffect(() => {
    console.log("Voice Agent ID in StepFour:", voiceAgentId);
  }, [voiceAgentId]);

  // Auto-scroll chat when messages change
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [chatMessages]);

  // Function to handle starting a call
  const handleStartCall = async () => {
    if (!voiceAgentId) {
      toast({
        title: "Error",
        description: "No voice agent ID found. Please restart the process.",
        variant: "destructive",
      });
      return;
    }

    setIsInitializing(true);

    try {
      await connectRTC(voiceAgentId);
      setIsCallActive(true);
      toast({
        title: "Connected",
        description: "You can now speak with the AI assistant.",
      });
    } catch (error) {
      console.error("Error starting call:", error);
      toast({
        title: "Error",
        description: "Failed to start call. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsInitializing(false);
    }
  };

  // Function to handle ending a call
  const handleEndCall = async () => {
    if (!voiceAgentId) {
      console.warn("No voiceAgentId available");
      return;
    }

    // If currentCallLog is missing, initialize with fallback (shouldn’t happen if connectRTC works)
    if (!currentCallLog) {
      console.warn("currentCallLog not found, initializing with fallback");
      currentCallLog = {
        voice_agent_id: voiceAgentId,
        call_start: new Date().toISOString(), // Fallback, but connectRTC should set this
        call_end: null,
        duration: null,
        transcript:
          transcriptMessages.length > 0 ? [...transcriptMessages] : [], // Use current transcriptMessages
        status: "terminated",
      };
    }

    // Signal end to API
    if (rtcDataChannel && rtcDataChannel.readyState === "open") {
      try {
        const endCallEvent = {
          type: "conversation.item.create",
          item: {
            type: "message",
            role: "user",
            content: [{ type: "input_text", text: "end call" }],
          },
        };
        rtcDataChannel.send(JSON.stringify(endCallEvent));
        await new Promise((resolve) => setTimeout(resolve, 1000));
        console.log("End call event sent");
        toast({ title: "Call Ended", description: "Call terminated" });
      } catch (error) {
        console.error("Error sending end call event:", error);
      }
    }

    // Update call log with end details
    currentCallLog.call_end = new Date().toISOString();
    if (currentCallLog.call_start && currentCallLog.call_end) {
      currentCallLog.duration = calculateDuration(
        currentCallLog.call_start,
        currentCallLog.call_end
      );
    }
    currentCallLog.transcript =
      transcriptMessages.length > 0 ? [...transcriptMessages] : []; // Ensure transcript is copied
    currentCallLog.status = "terminated";
    console.log("Manual end call log prepared:", currentCallLog);

    // Save log manually
    console.log("Request body being sent:", JSON.stringify(currentCallLog));
    await storeCallLog();

    // Proceed with cleanup
    endCall();
  };

  const handleToggleMute = () => {
    if (
      !localStreamRef.current ||
      !audioTrackRef.current ||
      !audioElementRef.current
    ) {
      console.error("Audio resources missing:", {
        stream: !!localStreamRef.current,
        track: !!audioTrackRef.current,
        audioEl: !!audioElementRef.current,
      });
      toast({
        title: "Error",
        description: "Audio resources unavailable. Please restart the call.",
        variant: "destructive",
      });
      setIsMuted(false); // Reset to a known state
      return;
    }

    try {
      const newMutedState = !isMuted; // Toggle based on current state
      audioTrackRef.current.enabled = !newMutedState; // Enable mic when unmuted
      audioElementRef.current.muted = newMutedState; // Mute audio when muted
      setIsMuted(newMutedState);

      console.log("Mute toggled:", {
        micEnabled: audioTrackRef.current.enabled,
        audioMuted: audioElementRef.current.muted,
        isMuted: newMutedState,
      });

      toast({
        title: newMutedState ? "Muted" : "Unmuted",
        description: newMutedState ? "Audio is muted." : "Audio is active.",
      });
    } catch (error) {
      console.error("Error toggling mute:", error);
      toast({
        title: "Error",
        description: "Failed to toggle mute.",
        variant: "destructive",
      });
      setIsMuted(false); // Reset on error
    }
  };
  // Function to append a message to chat (React way)
  const appendMessageToChat = (message: string, isAi = false) => {
    const timestamp = new Date().toISOString();
    const newMessage: ChatMessage = {
      text: message,
      isAi,
      timestamp: timestamp,
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    };

    // Use functional update to ensure we're working with latest state
    setChatMessages((prevMessages) => {
      const updatedMessages = [...prevMessages, newMessage];
      // Sort messages by timestamp to ensure proper sequence
      return updatedMessages.sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
    });
  };

  // =================== Global Variables ===================
  let localStream: MediaStream | null = null; // Global variable for user audio stream
  let pcConn: RTCPeerConnection | null = null; // Global peer connection object
  let greetingSent = false; // Flag to prevent duplicate greeting messages
  let functionData: FunctionData[] | null = null; // Stores backend function configuration data
  let currentCallLog: CallLog | null = null; // Object to temporarily store call log data
  // Add global transcript accumulator
  let aiTranscript = "";
  // Add global variable to temporarily store finished transcript messages with timestamp
  let transcriptMessages: TranscriptMessage[] = [];
  // Add new global variable for RTC data channel
  let rtcDataChannel: RTCDataChannel | null = null;

  // =================== Utility Functions ===================

  // Fetch the function configuration from the backend for a given agentId
  async function getFunctionData(agentId: string): Promise<FunctionData[]> {
    try {
      const response = await fetch(
        `https://build-operatorai-backend-production.up.railway.app/tools/function-data/${agentId}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch function data");
      }
      const data = await response.json();
      return data as FunctionData[];
    } catch (error) {
      console.error("Error fetching function data:", error);
      throw error;
    }
  }

  // Execute the function call based on OpenAI realtime instructions
  async function executeFunction(
    functionCall: FunctionCall,
    functionConfig: FunctionData[],
    voiceAgentId: string
  ) {
    try {
      // Handle end_call and get_current_time without configuration lookup
      if (functionCall.name === "end_call") {
        // Return response immediately
        const response = {
          status: "success",
          data: [
            {
              context:
                "Call ending initiated. Quickly say goodbye before it ends in 5 seconds.",
              metadata: {
                timestamp: new Date().toISOString(),
                action: "end_call",
              },
            },
          ],
        };

        // Set a timeout to end the call after 5 seconds
        setTimeout(() => {
          endCall();
        }, 8000);

        return response;
      }
      if (functionCall.name === "get_current_time") {
        const now = new Date();
        const daysOfWeek = [
          "Sunday",
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
        ];
        const day = daysOfWeek[now.getDay()];
        const offset = -now.getTimezoneOffset() / 60;
        const offsetStr = offset >= 0 ? `+${offset}` : `${offset}`;
        const formattedDateTime = now.toLocaleString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        });
        return {
          status: "success",
          data: [
            {
              context: `The current time is ${formattedDateTime} GMT${offsetStr}. Today is ${day}.`,
              metadata: {
                timestamp: now.toISOString(),
                action: "get_current_time",
                timeData: {
                  dayOfWeek: day,
                  formattedDateTime: formattedDateTime,
                  timeZone: `GMT${offsetStr}`,
                  iso8601: now.toISOString(),
                },
              },
            },
          ],
        };
      }

      // Find the matching function configuration
      const funcData = functionConfig.find((f) => f.name === functionCall.name);
      if (!funcData) {
        throw new Error(
          `No configuration found for function: ${functionCall.name}`
        );
      }

      const {
        data: { req_url, req_type, body, headers, query },
      } = funcData;
      const args = JSON.parse(functionCall.arguments);

      // Handle request based on method type and body requirements
      const requestOptions: RequestInit = {
        method: req_type,
        headers: { ...headers, "Content-Type": "application/json" },
      };

      // Handle POST, PATCH, PUT requests with body
      if (["POST", "PATCH", "PUT"].includes(req_type.toUpperCase()) && body) {
        const processedBody = { ...body }; // Clone the body object

        // Process each field in the body recursively
        const processBodyField = (obj: Record<string, BodyValue>) => {
          Object.keys(obj).forEach((key) => {
            const value = obj[key];

            if (
              typeof value === "object" &&
              value !== null &&
              !Array.isArray(value)
            ) {
              // Handle nested objects
              processBodyField(value as BodyObject);
            } else if (typeof value === "string") {
              // Handle string replacements
              obj[key] = value.replace(/{{voice_agent_id}}/g, voiceAgentId);

              // Replace other argument placeholders
              Object.entries(args).forEach(([argKey, argValue]) => {
                if (
                  typeof argValue === "string" ||
                  typeof argValue === "number" ||
                  typeof argValue === "boolean"
                ) {
                  obj[key] = (obj[key] as string).replace(
                    `{{${argKey}}}`,
                    argValue.toString()
                  );
                }
              });
            }
          });
        };

        processBodyField(processedBody);
        requestOptions.body = JSON.stringify(processedBody);
      }

      // Replace voice_agent_id in the base URL first
      let finalUrl = req_url.replace(/{{voice_agent_id}}/g, voiceAgentId);

      // Handle GET requests with query parameters
      if (
        req_type.toUpperCase() === "GET" &&
        (query || Object.keys(args).length > 0)
      ) {
        const queryParams = new URLSearchParams();

        if (query) {
          const queryConfig = { ...query };
          Object.entries(queryConfig).forEach(([key, value]) => {
            let processedValue = value
              .toString()
              .replace(/{{voice_agent_id}}/g, voiceAgentId);

            Object.entries(args).forEach(([argKey, argValue]) => {
              if (
                typeof argValue === "string" ||
                typeof argValue === "number" ||
                typeof argValue === "boolean"
              ) {
                processedValue = processedValue.replace(
                  `{{${argKey}}}`,
                  argValue.toString()
                );
              }
            });
            queryParams.append(key, processedValue);
          });
        }

        // Add remaining args to query params
        Object.entries(args).forEach(([key, argValue]) => {
          if (
            !queryParams.has(key) &&
            (typeof argValue === "string" ||
              typeof argValue === "number" ||
              typeof argValue === "boolean")
          ) {
            queryParams.append(key, argValue.toString());
          }
        });

        finalUrl = `${finalUrl}${
          finalUrl.includes("?") ? "&" : "?"
        }${queryParams.toString()}`;
      }

      console.log(`Executing ${functionCall.name} with URL:`, finalUrl);

      const response = await fetch(finalUrl, requestOptions);
      console.log(`${functionCall.name} Response Status:`, response.status);

      if (!response.ok) {
        throw new Error(`API call failed with status: ${response.status}`);
      }

      const result = await response.json();
      console.log(`${functionCall.name} Response Data:`, result);

      // Format the response in a consistent way for the AI
      return {
        status: "success",
        data: [
          {
            context:
              typeof result === "string"
                ? result
                : result.bookedSlots
                ? JSON.stringify(result.bookedSlots)
                : Array.isArray(result)
                ? result
                    .map((item: { context?: string }) => item.context || item)
                    .join("\n")
                : JSON.stringify(result),
            metadata: {
              timestamp: new Date().toISOString(),
              action: functionCall.name,
            },
          },
        ],
      };
    } catch (error) {
      console.error("Function execution error:", error);
      return {
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  // Updated helper to compute call duration in a human-readable format (e.g., "2 min 8 sec 203 ms")
  function calculateDuration(callStart: string, callEnd: string): string {
    const diffMillis =
      new Date(callEnd).getTime() - new Date(callStart).getTime();
    const minutes = Math.floor(diffMillis / 60000);
    const seconds = Math.floor((diffMillis % 60000) / 1000);
    const milliseconds = diffMillis % 1000;
    return `${minutes} min ${seconds} sec ${milliseconds} ms`;
  }

  // End the call, close peer connection and release audio resources
  function endCall(): void {
    greetingSent = false;

    // Only save if not already saved (e.g., by handleEndCall)
    if (currentCallLog && !currentCallLog.call_end) {
      currentCallLog.call_end = new Date().toISOString();
      if (currentCallLog.call_start && currentCallLog.call_end) {
        currentCallLog.duration = calculateDuration(
          currentCallLog.call_start,
          currentCallLog.call_end
        );
      }
      currentCallLog.transcript =
        transcriptMessages.length > 0 ? [...transcriptMessages] : null;
      if (!currentCallLog.status) {
        currentCallLog.status = "terminated"; // Default if not set
      }
      console.log("EndCall saving log (if not already saved):", currentCallLog);
      storeCallLog();
    }

    // Cleanup
    currentCallLog = null;
    transcriptMessages = [];
    if (rtcDataChannel && rtcDataChannel.readyState !== "closed") {
      rtcDataChannel.close();
      console.log("RTC Data Channel closed");
      rtcDataChannel = null;
    }
    if (pcConn && pcConn.connectionState !== "closed") {
      pcConn.close();
      console.log("Peer Connection closed");
      pcConn = null;
    }
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach((track) => track.stop());
      localStreamRef.current = null;
      audioTrackRef.current = null;
    }
    if (audioElementRef.current) {
      audioElementRef.current.pause();
      audioElementRef.current.srcObject = null;
      audioElementRef.current.remove();
      console.log("Remote audio stopped");
      audioElementRef.current = null;
    }
    aiTranscript = "";
    setChatMessages([]);
    setIsCallActive(false);
    setIsSpeaking(false);
    setIsUserSpeaking(false);
    setIsMuted(false);
  }

  // Function to send the stored call log to the backend
  async function storeCallLog(): Promise<void> {
    if (!currentCallLog) return;

    const requestBody = JSON.stringify(currentCallLog);
    console.log("Request body being sent:", requestBody);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;
      if (!apiUrl) {
        throw new Error("API URL is not defined");
      }

      await fetch(`${apiUrl}/call-logs`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: requestBody,
      });
      console.log("Call log stored successfully.");
    } catch (error) {
      console.error("Error storing call log:", error);
    }
  }

  const audioElementRef = useRef<HTMLAudioElement | null>(null);

  // =================== RTC Connection Setup ===================
  //initializes RTC connection, fetches data, and sets up events.
  async function connectRTC(agentId: string): Promise<void> {
    // Fetch function data

    try {
      functionData = await getFunctionData(agentId);
      console.log("Fetched function data:", functionData);
    } catch (error) {
      console.log(error);
      toast({
        title: "Error",
        description: "Failed to fetch function data.",
        variant: "destructive",
      });
      return;
    }

    // Get ephemeral key from server
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (!apiUrl) {
      throw new Error("API URL is not defined");
    }

    const tokenResponse = await fetch(`${apiUrl}/realtime/session/${agentId}`);
    const data = await tokenResponse.json();
    const EPHEMERAL_KEY = data.client_secret.value;

    // Create the peer connection and assign it globally
    const pc = new RTCPeerConnection();
    pcConn = pc;

    // Set up remote audio
    const audioEl = document.createElement("audio");
    audioEl.autoplay = true;
    pc.ontrack = (e) => {
      audioEl.srcObject = e.streams[0];
    };

    // Set up local audio stream
    localStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    localStreamRef.current = localStream;
    const audioTrack = localStream.getAudioTracks()[0];
    audioTrackRef.current = audioTrack; // Store track in ref for mute/unmute
    pc.addTrack(audioTrack);

    // Set up audio level detection for user
    const audioContextClass =
      (window as AudioContextFallback).AudioContext ||
      (window as AudioContextFallback).webkitAudioContext;
    const audioContext = new audioContextClass!();
    const source = audioContext.createMediaStreamSource(localStream);
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 256;
    source.connect(analyser);

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    // Check audio levels periodically
    const checkAudioInterval = setInterval(() => {
      if (!isCallActive) {
        clearInterval(checkAudioInterval);
        return;
      }

      analyser.getByteFrequencyData(dataArray);
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i];
      }

      const average = sum / bufferLength;
      // Only update speaking status if not muted
      if (!isMuted) {
        setIsUserSpeaking(average > 20); // Threshold for detecting speech
      } else {
        setIsUserSpeaking(false);
      }
    }, 100);

    audioElementRef.current = document.createElement("audio");
    audioElementRef.current.autoplay = true;
    pc.ontrack = (e) => {
      audioElementRef.current!.srcObject = e.streams[0];
    };

    currentCallLog = {
      voice_agent_id: agentId,
      call_start: new Date().toISOString(),
      call_end: null,
      duration: null,
      transcript: [], // Start with empty array, not null
      status: "completed",
    };
    console.log("Call log initialized at start:", currentCallLog);
    // Set up RTC data channel and its event listener
    const dc = pc.createDataChannel("oai-events");
    rtcDataChannel = dc; // Assign data channel to global variable
    dc.addEventListener("message", async (e) => {
      const realtimeEvent = JSON.parse(e.data);
      console.log("Realtime Event:", realtimeEvent);

     
      // User transcript
      if (
        realtimeEvent.type ===
        "conversation.item.input_audio_transcription.completed"
      ) {
        if (realtimeEvent.transcript) {
          const currentTime = new Date().toISOString();
          const userTranscript = realtimeEvent.transcript.trim();
          transcriptMessages.push({
            role: "user",
            transcript: "User: " + userTranscript,
            time: currentTime,
          });
          console.log("User transcript stored:", userTranscript);
          setIsUserSpeaking(false);
          appendMessageToChat(userTranscript, false);
        }
        return;
      }

      // AI transcript delta
      if (realtimeEvent.type === "response.audio_transcript.delta") {
        aiTranscript += realtimeEvent.delta;
        setIsSpeaking(true);
        // Existing chat update logic...
        setChatMessages((prev) => {
          const lastAiMessageIndex = [...prev]
            .reverse()
            .findIndex((msg) => msg.isAi);
          if (lastAiMessageIndex !== -1 && lastAiMessageIndex < 3) {
            const actualIndex = prev.length - 1 - lastAiMessageIndex;
            const lastMessage = prev[actualIndex];
            const timeDiff =
              new Date().getTime() - new Date(lastMessage.timestamp).getTime();
            if (timeDiff < 3000) {
              const newMessages = [...prev];
              newMessages[actualIndex] = {
                ...lastMessage,
                text: aiTranscript,
                timestamp: new Date().toISOString(),
              };
              return newMessages.sort(
                (a, b) =>
                  new Date(a.timestamp).getTime() -
                  new Date(b.timestamp).getTime()
              );
            }
          }
          const timestamp = new Date().toISOString();
          return [
            ...prev,
            {
              text: aiTranscript,
              isAi: true,
              timestamp,
              id: `msg-${Date.now()}-${Math.random()
                .toString(36)
                .substring(2, 9)}`,
            },
          ].sort(
            (a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
        });
        return;
      }

      // AI transcript done
      if (realtimeEvent.type === "response.audio_transcript.done") {
        const currentTime = new Date().toISOString();
        const finalTranscript = realtimeEvent.transcript || aiTranscript;
        transcriptMessages.push({
          role: "ai",
          transcript: "AI: " + finalTranscript,
          time: currentTime,
        });
        console.log("AI transcript stored:", finalTranscript);
        // Existing chat update logic...
        setChatMessages((prev) => {
          const lastAiMessageIndex = [...prev]
            .reverse()
            .findIndex((msg) => msg.isAi);
          if (lastAiMessageIndex !== -1) {
            const actualIndex = prev.length - 1 - lastAiMessageIndex;
            const updatedMessages = [...prev];
            updatedMessages[actualIndex] = {
              ...updatedMessages[actualIndex],
              text: finalTranscript,
              timestamp: currentTime,
            };
            return updatedMessages.sort(
              (a, b) =>
                new Date(a.timestamp).getTime() -
                new Date(b.timestamp).getTime()
            );
          }
          return [
            ...prev,
            {
              text: finalTranscript,
              isAi: true,
              timestamp: currentTime,
              id: `msg-${Date.now()}-${Math.random()
                .toString(36)
                .substring(2, 9)}`,
            },
          ].sort(
            (a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
        });
        aiTranscript = "";
        setIsSpeaking(false);
        return;
      }
      // Add handler for user input transcription
      if (
        realtimeEvent.type ===
        "conversation.item.input_audio_transcription.completed"
      ) {
        if (realtimeEvent.transcript) {
          const currentTime = new Date().toISOString();
          const userTranscript = realtimeEvent.transcript.trim();

          // Store in transcript messages array with timestamp
          transcriptMessages.push({
            role: "user",
            transcript: "User: " + userTranscript,
            time: currentTime,
          });

          console.log("User transcript stored:", userTranscript);
          setIsUserSpeaking(false);

          // Add to chat display
          appendMessageToChat(userTranscript, false);
        }
        return;
      }

      if (
        (realtimeEvent.type === "session.created" ||
          realtimeEvent.type === "session.updated") &&
        !greetingSent
      ) {
        greetingSent = true;
        const greetingEvent = {
          type: "conversation.item.create",
          item: {
            type: "message",
            role: "user",
            content: [
              {
                type: "input_text",
                text: "Introduce yourself by saying your name and greeting the customer, patient, or client appropriately based on the business type and context.",
              },
            ],
          },
        };
        dc.send(JSON.stringify(greetingEvent));
        const responseEvent = {
          type: "response.create",
          response: { modalities: ["audio", "text"] },
        };
        dc.send(JSON.stringify(responseEvent));
      }
      if (realtimeEvent.type === "error") {
        greetingSent = false;
      }
      if (
        realtimeEvent.type === "response.done" &&
        realtimeEvent.response?.output?.[0]?.type === "function_call"
      ) {
        const fc = realtimeEvent.response.output[0];
        if (functionData) {
          const result = await executeFunction(
            fc,
            functionData,
            voiceAgentId || ""
          );
          console.log("Function Result:", result);

          let contextInfo;
          if ("error" in result) {
            contextInfo = `Error: ${result.error}`;
          } else if (result.data && result.data[0]) {
            contextInfo = result.data[0].context;
          } else {
            contextInfo = "No relevant information found.";
          }

          const functionResult = {
            type: "conversation.item.create",
            item: {
              type: "function_call_output",
              call_id: fc.call_id,
              output: JSON.stringify({ information: contextInfo }),
            },
          };

          console.log("Sending Function Result:", functionResult);
          dc.send(JSON.stringify(functionResult));
          dc.send(JSON.stringify({ type: "response.create" }));
        }
        return;
      }

      // Handle audio generation start
      if (realtimeEvent.type === "audio.generation.start") {
        setIsSpeaking(true);
        return;
      }

      // Handle audio generation end
      if (realtimeEvent.type === "audio.generation.end") {
        setIsSpeaking(false);
        return;
      }

      // Handle audio transcript delta updates: accumulate transcript text
      if (realtimeEvent.type === "response.audio_transcript.delta") {
        aiTranscript += realtimeEvent.delta;
        setIsSpeaking(true);

        // Update the last AI message in our React state if it exists, or create a new one
        setChatMessages((prev) => {
          // Find the last AI message if it exists
          const lastAiMessageIndex = [...prev]
            .reverse()
            .findIndex((msg) => msg.isAi);

          // Check if the last message is from AI and was created recently (within last 3 seconds)
          if (lastAiMessageIndex !== -1 && lastAiMessageIndex < 3) {
            const reversedIndex = lastAiMessageIndex;
            const actualIndex = prev.length - 1 - reversedIndex;
            const lastMessage = prev[actualIndex];

            const now = new Date();
            const messageTime = new Date(lastMessage.timestamp);
            const timeDiff = now.getTime() - messageTime.getTime();

            // If the message is recent (less than 3 seconds old), update it
            if (timeDiff < 3000) {
              const newMessages = [...prev];
              newMessages[actualIndex] = {
                ...lastMessage,
                text: aiTranscript,
                timestamp: new Date().toISOString(),
              };
              return newMessages.sort(
                (a, b) =>
                  new Date(a.timestamp).getTime() -
                  new Date(b.timestamp).getTime()
              );
            }
          }

          // Otherwise, create a new AI message
          const timestamp = new Date().toISOString();
          return [
            ...prev,
            {
              text: aiTranscript,
              isAi: true,
              timestamp: timestamp,
              id: `msg-${Date.now()}-${Math.random()
                .toString(36)
                .substring(2, 9)}`,
            },
          ].sort(
            (a, b) =>
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
        });

        return;
      }

      // Handle final audio transcript done event
      if (realtimeEvent.type === "response.audio_transcript.done") {
        const currentTime = new Date().toISOString();
        const finalTranscript = realtimeEvent.transcript || aiTranscript;

        // Store in transcript messages array with timestamp
        transcriptMessages.push({
          role: "ai",
          transcript: "AI: " + finalTranscript,
          time: currentTime,
        });

        // Ensure the final message appears in the chat
        setChatMessages((prev) => {
          // Check if we need to update an existing message or add a new one
          const lastAiMessageIndex = [...prev]
            .reverse()
            .findIndex((msg) => msg.isAi);

          if (lastAiMessageIndex !== -1) {
            const reversedIndex = lastAiMessageIndex;
            const actualIndex = prev.length - 1 - reversedIndex;

            // Update the existing message with the final transcript
            const updatedMessages = [...prev];
            updatedMessages[actualIndex] = {
              ...updatedMessages[actualIndex],
              text: finalTranscript,
              timestamp: currentTime,
            };

            return updatedMessages.sort(
              (a, b) =>
                new Date(a.timestamp).getTime() -
                new Date(b.timestamp).getTime()
            );
          } else {
            // Add a new message if no AI message exists
            const newMessage = {
              text: finalTranscript,
              isAi: true,
              timestamp: currentTime,
              id: `msg-${Date.now()}-${Math.random()
                .toString(36)
                .substring(2, 9)}`,
            };

            return [...prev, newMessage].sort(
              (a, b) =>
                new Date(a.timestamp).getTime() -
                new Date(b.timestamp).getTime()
            );
          }
        });

        // Reset accumulator for future transcripts
        aiTranscript = "";
        setIsSpeaking(false);
        return;
      }
    });

    // Start session using SDP
    const offer = await pc.createOffer();
    await pc.setLocalDescription(offer);
    const baseUrl = "https://api.openai.com/v1/realtime";
    const model = "gpt-4o-realtime-preview-2024-12-17";
    const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
      method: "POST",
      body: offer.sdp,
      headers: {
        Authorization: `Bearer ${EPHEMERAL_KEY}`,
        "Content-Type": "application/sdp",
      },
    });
    const answer: RTCSessionDescriptionInit = {
      type: "answer",
      sdp: await sdpResponse.text(),
    };
    await pc.setRemoteDescription(answer);
    console.log("Connected to Realtime API for voice-to-voice interaction.");
  }

  useEffect(() => {
    return () => {
      if (isCallActive) {
        endCall();
      }
    };
  }, [isCallActive]);

  return (
    <Card className="w-full border-none shadow-none">
      <CardHeader>
        <CardTitle className="text-center text-3xl font-bold">
          Try Out {agentName || "Your Voice Assistant"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-center text-gray-600 mb-8">
          Ask your chatbot a question using the selected sources. Once
          you&apos;re satisfied, share or embed it on your site. You can always
          add more sources later to enhance its performance!
        </p>
        <div className="flex flex-col items-center space-y-8">
          <div className="w-full max-w-2xl bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            {/* Agent Header */}
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold">
                Voice Agent {voiceAgentId}
              </h3>
            </div>

            <Card className="w-full max-w-lg mx-auto rounded-3xl overflow-hidden shadow-lg border-[22px]  border-[#E6E7EB]">
              {/* Header */}
              <div className="bg-white border-b border-gray-100 p-4">
                <h2 className="text-center text-xl font-bold">AI</h2>
              </div>

              {/* Chat UI */}
              <CardContent className="p-0 flex flex-col h-[600px] bg-white ">
                {/* AI Agent Header Info */}
                <div className="flex flex-col items-center justify-center py-6">
                  <div className="w-16 h-16 rounded-full flex items-center justify-center mb-2 relative">
                    <Image
                      src={logo}
                      alt="AI Assistant"
                      width={52}
                      height={52}
                    />
                    {/* Connection Status Indicator */}
                    {isCallActive && (
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center border-2 border-white">
                        <div className={isSpeaking ? "animate-pulse" : ""}>
                          <Volume2 className="w-3 h-3 text-white" />
                        </div>
                      </div>
                    )}
                  </div>
                  <h3 className="text-center font-semibold">
                    AI Agent answers
                  </h3>
                  <p className="text-center text-sm text-gray-500">instantly</p>

                  {/* Connection Status Text */}
                  {isCallActive && (
                    <div className="mt-2 flex items-center text-xs text-green-600 font-medium">
                      <span className="w-2 h-2 rounded-full bg-green-500 mr-1 animate-pulse"></span>
                      Connected {isMuted && "- Muted"}
                    </div>
                  )}
                </div>

                {/* Chat Messages Area */}
                <ScrollArea className="flex-grow px-4 py-2 " ref={scrollAreaRef}>
                  {chatMessages.length > 0 ? (
                    chatMessages.map((msg, index) => (
                      <div
                        key={msg.id || index}
                        className={`flex mb-3 ${
                          msg.isAi ? "justify-start" : "justify-end"
                        }`}
                      >
                        {msg.isAi ? (
                          <div className="flex items-start">
                            <div
                              className={`h-8 w-8 rounded-full flex items-center justify-center mr-2 ${
                                isSpeaking &&
                                index === chatMessages.length - 1 &&
                                msg.isAi
                                  ? "animate-pulse"
                                  : ""
                              }`}
                            >
                              <Image
                                src={logo}
                                alt="AI agent"
                                width={24}
                                height={24}
                              />
                            </div>
                            <div className="max-w-[75%] p-3 rounded-lg bg-gray-100 text-gray-800 relative group">
                              <p className="text-sm">{msg.text}</p>
                              <span className="absolute -bottom-4 left-0 text-[10px] text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                                {new Date(msg.timestamp).toLocaleTimeString(
                                  [],
                                  {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  }
                                )}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-start justify-end">
                            <div className="max-w-[75%] p-3 rounded-lg bg-gray-100/80 text-gray-800 relative group">
                              <p className="text-sm">{msg.text}</p>
                              <span className="absolute -bottom-4 right-0 text-[10px] text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                                {new Date(msg.timestamp).toLocaleTimeString(
                                  [],
                                  {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  }
                                )}
                              </span>
                            </div>
                            <div
                              className={`h-8 w-8 rounded-full  flex items-center justify-center ml-2 ${
                                isUserSpeaking &&
                                index === chatMessages.length - 1 &&
                                !msg.isAi
                                  ? "animate-pulse"
                                  : ""
                              }`}
                            >
                              <Image
                                src={agent}
                                alt="agent"
                                width={24}
                                height={24}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="flex-grow flex flex-col items-center justify-center">
                      <div className="p-8 text-center">
                        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gray-50 flex items-center justify-center">
                          <svg
                            width="32"
                            height="32"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M12 15.5c-2.21 0-4-1.79-4-4V6c0-2.21 1.79-4 4-4s4 1.79 4 4v5.5c0 2.21-1.79 4-4 4z"
                              stroke="#d1d5db"
                              strokeWidth="2"
                            />
                            <path
                              d="M6 9.5H4c0 4.42 3.58 8 8 8s8-3.58 8-8h-2"
                              stroke="#d1d5db"
                              strokeWidth="2"
                              strokeLinecap="round"
                            />
                            <path
                              d="M12 19.5v2"
                              stroke="#d1d5db"
                              strokeWidth="2"
                              strokeLinecap="round"
                            />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-gray-700 mb-2">
                          Start a conversation
                        </h3>
                        <p className="text-sm text-gray-500 mb-6">
                          Tap the call button below to speak with the AI
                          assistant
                        </p>
                        <div className="h-1 w-16 bg-gray-100 mx-auto rounded-full"></div>
                      </div>
                    </div>
                  )}
                </ScrollArea>

                {/* Call Controls Area */}
                <div className="p-4 flex justify-center items-center gap-4">
                  {!isCallActive && (
                    <Button
                      onClick={handleStartCall}
                      className="w-16 h-16 rounded-full bg-[#E74F13] hover:bg-[#E74F13]/80 flex items-center justify-center shadow-md"
                      disabled={isInitializing}
                    >
                      {isInitializing ? (
                        <Loader2 className="h-8 w-8 text-white animate-spin" />
                      ) : (
                        <Phone className="h-8 w-8 text-white" />
                      )}
                    </Button>
                  )}

                  {isCallActive && (
                    <>
                      {/* Mute/Unmute Button */}
                      {isCallActive && (
                        <>
                          <Button
                            onClick={handleToggleMute}
                            className={`w-16 h-16 rounded-full ${
                              isMuted ? "bg-gray-500" : "bg-blue-500"
                            } hover:bg-opacity-80 flex items-center justify-center shadow-md`}
                          >
                            {isMuted ? (
                              <MicOff className="h-8 w-8 text-white" />
                            ) : (
                              <Mic className="h-8 w-8 text-white" />
                            )}
                          </Button>
                          <Button
                            onClick={handleEndCall}
                            className="w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center shadow-md"
                          >
                            <PhoneOff className="h-8 w-8 text-white" />
                          </Button>
                        </>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-center gap-4">
          <Button variant="outline" className="px-6 h-12 rounded-lg">
            <Link href="/dashboard/test-assistant">View Demo On Website</Link>
          </Button>
          <Button className="px-6 h-12 bg-[#E74F13] hover:bg-[#E74F13]/90 text-white rounded-lg">
            <Link href="/dashboard/settings">Customize & Install</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default StepFour;
