import { create } from "zustand";

interface TrainingStore {
  // URLs state
  scannedUrls: string[];
  isLoading: boolean;
  error: string | null;
  scrapeProgress: number;
  streamStatus: string;
  scrappingId: string;

  // PDF files state
  uploadedFiles: File[];

  // URL actions
  setScannedUrls: (urls: string[]) => void;
  addUrl: (url: string) => void;
  removeUrl: (url: string) => void;
  setScrappingId: (id: string) => void;
  setStreamStatus: (status: string) => void;
  setScrapeProgress: (progress: number) => void;
  setError: (error: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;

  // PDF file actions
  addUploadedFile: (file: File) => void;
  addUploadedFiles: (files: File[]) => void;
  removeUploadedFile: (index: number) => void;
  clearUploadedFiles: () => void;

  // Reset state
  resetTrainingState: () => void;
}

const initialState = {
  scannedUrls: [],
  isLoading: false,
  error: null,
  scrapeProgress: 0,
  streamStatus: "",
  scrappingId: "",
  uploadedFiles: [],
};

const useTrainingStore = create<TrainingStore>((set) => ({
  ...initialState,

  // URL-related setters
  setScannedUrls: (urls) => set({ scannedUrls: urls }),
  addUrl: (url) =>
    set((state) => ({ scannedUrls: [...state.scannedUrls, url] })),
  removeUrl: (url) =>
    set((state) => ({
      scannedUrls: state.scannedUrls.filter((u) => u !== url),
    })),
  setScrappingId: (id) => set({ scrappingId: id }),
  setStreamStatus: (status) => set({ streamStatus: status }),
  setScrapeProgress: (progress) => set({ scrapeProgress: progress }),
  setError: (error) => set({ error }),
  setIsLoading: (isLoading) => set({ isLoading }),

  // PDF file-related actions
  addUploadedFile: (file) =>
    set((state) => ({ uploadedFiles: [...state.uploadedFiles, file] })),
  addUploadedFiles: (files) =>
    set((state) => ({ uploadedFiles: [...state.uploadedFiles, ...files] })),
  removeUploadedFile: (index) =>
    set((state) => ({
      uploadedFiles: state.uploadedFiles.filter((_, i) => i !== index),
    })),
  clearUploadedFiles: () => set({ uploadedFiles: [] }),

  // Reset entire state
  resetTrainingState: () => set(initialState),
}));

export default useTrainingStore;
