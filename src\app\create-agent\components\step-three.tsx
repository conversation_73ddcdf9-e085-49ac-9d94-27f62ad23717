import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Loader2,
  Check,
  Trash2,
  Link,
  FileText,
  ExternalLink,
  Globe,
  Info,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import useTrainingStore from "@/store/features/useTrainingStore";
import useAgentStore from "@/store/features/useAgentStore";
import {
  useInitTrainingMutation,
  useScrapeUrlsMutation,
  useUploadTrainingFilesMutation,
} from "@/store/features/trainingApi";
import { cancelScan, scanUrl } from "@/store/features/url-scanner-service";
import PDFDialog from "./pdf-dialog";
import {
  Toolt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Define interfaces for component props
interface URLDialogProps {
  onURLSubmit: (url: string) => Promise<void>;
  onScrapedURLsSubmit: (urls: string[]) => Promise<void>;
  isLoading: boolean;
  scrapeProgress: number;
  streamStatus: string;
  onCancel: () => void;
  onOpen: () => void;
}

interface SummaryCardProps {
  urlCount: number;
  fileCount: number;
}

interface StepThreeProps {
  onNextStep: () => void;
}

// URL Dialog Component
const URLDialog: React.FC<URLDialogProps> = ({
  onURLSubmit,
  onScrapedURLsSubmit,
  isLoading,
  scrapeProgress,
  streamStatus,
  onCancel,
  onOpen,
}) => {
  const { toast } = useToast();
  const [url, setUrl] = useState("");
  const [isUrlValid, setIsUrlValid] = useState(false);
  const [scrapedUrls, setScrapedUrls] = useState("");
  const [activeTab, setActiveTab] = useState("scan");

  useEffect(() => {
    if (scrapeProgress === 100) {
      setTimeout(() => {
        onCancel();
      }, 1000);
    }
  }, [scrapeProgress, onCancel]);

  const handleURLChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputUrl = e.target.value;
    setUrl(inputUrl);
    try {
      const urlObj = new URL(inputUrl);
      setIsUrlValid(
        urlObj.protocol === "http:" || urlObj.protocol === "https:"
      );
    } catch (error) {
      console.log(error)
      setIsUrlValid(false);
    }
  };

  const handleScrapedUrlsChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setScrapedUrls(e.target.value);
  };

  const handleScanSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (url && isUrlValid) {
      await onURLSubmit(url);
    }
  };

  const handleDirectUrlsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (scrapedUrls.trim()) {
      const urlList = scrapedUrls
        .split("\n")
        .map((url) => url.trim())
        .filter((url) => url);
      if (urlList.length === 0) {
        toast({
          title: "No valid URLs",
          description: "Please enter at least one valid URL",
          variant: "destructive",
        });
        return;
      }
      await onScrapedURLsSubmit(urlList);
      onCancel();
    }
  };

  useEffect(() => {
    onOpen();
  }, []);

  return (
    <DialogContent className="sm:max-w-md">
      <div className="flex flex-col items-center p-6">
        <div className="w-12 h-12 bg-[#E74F13] rounded-full flex items-center justify-center mb-4">
          <Globe className="w-6 h-6 text-white" />
        </div>
        <h2 className="text-xl font-semibold mb-2">Add Your Website or URLs</h2>
        <p className="text-gray-500 text-center mb-6">
          Train your chatbot by scanning a website or adding specific URLs
          directly.
        </p>

        <Tabs
          defaultValue="scan"
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="scan">Scan Website</TabsTrigger>
            <TabsTrigger value="direct">Add URLs Directly</TabsTrigger>
          </TabsList>

          <TabsContent value="scan" className="w-full">
            <form onSubmit={handleScanSubmit} className="w-full space-y-6">
              <div className="space-y-2">
                <Input
                  placeholder="Enter website URL"
                  type="url"
                  value={url}
                  onChange={handleURLChange}
                  disabled={isLoading}
                  className="w-full rounded-lg h-12"
                />
                <p className="text-sm text-gray-500 pl-1">
                  Provide website URL to scan
                </p>
              </div>

              {isLoading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{streamStatus}</span>
                    <span>{scrapeProgress}%</span>
                  </div>
                  <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-[#E74F13] transition-all duration-300 ease-in-out"
                      style={{ width: `${scrapeProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="flex space-x-3">
                {isLoading && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    className="flex-1 h-12 rounded-lg"
                  >
                    Cancel Scan
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={!isUrlValid || isLoading}
                  className={`${
                    isLoading ? "flex-1" : "w-full"
                  } bg-[#E74F13] hover:bg-[#E74F13]/90 rounded-lg h-12`}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <Loader2 className="animate-spin mr-2 h-4 w-4" />
                      Scanning...
                    </div>
                  ) : (
                    "Scan Website"
                  )}
                </Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="direct" className="w-full">
            <form
              onSubmit={handleDirectUrlsSubmit}
              className="w-full space-y-6"
            >
              <div className="space-y-2">
                <Textarea
                  placeholder="Enter URLs (one per line)"
                  value={scrapedUrls}
                  onChange={handleScrapedUrlsChange}
                  className="w-full rounded-lg min-h-[150px]"
                />
                <p className="text-sm text-gray-500 pl-1">
                  Enter each URL on a new line
                </p>
              </div>
              <Button
                type="submit"
                disabled={!scrapedUrls.trim()}
                className="w-full bg-[#E74F13] hover:bg-[#E74F13]/90 rounded-lg h-12"
              >
                Add URLs
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </div>
    </DialogContent>
  );
};

// Summary Card component
const TrainingSummaryCard: React.FC<SummaryCardProps> = ({
  urlCount,
  fileCount,
}) => {
  return (
    <div className="bg-white border border-gray-100 rounded-lg p-4 shadow-sm mb-6 max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-semibold">Training Source Summary</h3>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Info size={16} className="text-gray-400" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs max-w-xs">
                Your agent will learn from these sources to better understand
                your content and respond to user queries.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="grid grid-cols-2 gap-4 mt-4">
        <div className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
          <Globe className="h-6 w-6 text-[#E74F13] mb-1" />
          <span className="text-2xl font-bold">{urlCount}</span>
          <span className="text-sm text-gray-500">Websites</span>
        </div>
        <div className="flex flex-col items-center p-3 bg-gray-50 rounded-lg">
          <FileText className="h-6 w-6 text-blue-600 mb-1" />
          <span className="text-2xl font-bold">{fileCount}</span>
          <span className="text-sm text-gray-500">Documents</span>
        </div>
      </div>
    </div>
  );
};

const StepThree: React.FC<StepThreeProps> = ({ onNextStep }) => {
  const {
    scannedUrls,
    isLoading: isScanningUrl,
    scrapeProgress,
    streamStatus,
    scrappingId,
    removeUrl,
    addUrl,
    uploadedFiles,
    removeUploadedFile,
    setScrappingId,
    setScrapeProgress,
  } = useTrainingStore();

  const voiceAgentId = useAgentStore((state) => state.voiceAgentId);
  const agentName = useAgentStore((state) => state.agentName);
  const { toast } = useToast();

  const [initTraining, { isLoading: isTrainingUrls }] =
    useInitTrainingMutation();
  const [uploadTrainingFiles, { isLoading: isUploadingFiles }] =
    useUploadTrainingFilesMutation();
  const [scrapeUrls, { isLoading: isScraping }] = useScrapeUrlsMutation();

  const [selectedUrls, setSelectedUrls] = useState<string[]>([]);
  const [trainingStatus, setTrainingStatus] = useState("");
  const [isUrlDialogOpen, setIsUrlDialogOpen] = useState(false);
  const [isPdfDialogOpen, setIsPdfDialogOpen] = useState(false);
  const [isTraining, setIsTraining] = useState(false);
  const [showSummary, setShowSummary] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0); // New state for training progress

  useEffect(() => {
    setSelectedUrls([...scannedUrls]);
  }, [scannedUrls]);

  useEffect(() => {
    setShowSummary(scannedUrls.length > 0 || uploadedFiles.length > 0);
  }, [scannedUrls.length, uploadedFiles.length]);

  useEffect(() => {
    if (isScanningUrl && !isUrlDialogOpen) {
      setIsUrlDialogOpen(true);
    }
  }, [isScanningUrl, isUrlDialogOpen]);

  // Simulate training progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTraining) {
      setTrainingProgress(0); // Reset progress when training starts
      interval = setInterval(() => {
        setTrainingProgress((prev) => {
          if (prev >= 90) {
            return prev; // Stop at 90% until training completes
          }
          return prev + 10; // Increment by 10% every 500ms
        });
      }, 500);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTraining]);

  const handleRemoveUrl = (url: string) => {
    setSelectedUrls((prev) => prev.filter((u) => u !== url));
  };

  const handleRemoveFromScan = (url: string) => {
    removeUrl(url);
    handleRemoveUrl(url);
  };

  const handleScanUrl = async (url: string) => {
    try {
      await scanUrl(url);
    } catch (error) {
      toast({
        title: "Error scanning URL",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const handleScrapedURLsSubmit = async (urlList: string[]) => {
    try {
      const response = await scrapeUrls({ urls: urlList }).unwrap();
      console.log("Scrape response:", response);
      urlList.forEach((url) => addUrl(url));
      if (response.scrapping_id) {
        setScrappingId(response.scrapping_id);
      } else {
        toast({
          title: "Warning",
          description: "Scraping completed but no scrapping ID returned.",
          variant: "destructive",
        });
      }
      toast({
        title: "URLs added successfully",
        description: `Added ${urlList.length} URLs for training`,
      });
    } catch (error) {
      console.error("Error adding URLs:", error);
      toast({
        title: "Error adding URLs",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const handleRemovePdf = (index: number) => {
    removeUploadedFile(index);
  };

  const handleCancelScan = () => {
    cancelScan();
    setIsUrlDialogOpen(false);
    setScrapeProgress(0);
  };

  const handleDialogOpen = () => {
    setScrapeProgress(0);
  };

  const handleStartTraining = async () => {
    console.log("Training inputs:", {
      voiceAgentId,
      scrappingId,
      selectedUrls,
      uploadedFiles,
    });

    if (!voiceAgentId) {
      toast({
        title: "Error",
        description:
          "No voice agent ID found. Please go back and create an agent first.",
        variant: "destructive",
      });
      return;
    }

    const hasUrls = selectedUrls.length > 0;
    const hasFiles = uploadedFiles.length > 0;

    if (!hasUrls && !hasFiles) {
      toast({
        title: "Error",
        description:
          "Please select at least one URL or upload a PDF file for training.",
        variant: "destructive",
      });
      return;
    }

    setIsTraining(true);
    setTrainingStatus("Initializing training...");
    let success = true;

    try {
      if (hasUrls) {
        if (!scrappingId) {
          toast({
            title: "Error",
            description:
              "Scrapping ID is missing for URL training. Please re-scrape URLs.",
            variant: "destructive",
          });
          setIsTraining(false);
          return;
        }
        setTrainingStatus("Processing website data...");
        try {
          await initTraining({
            voiceAgentId,
            data: { data: selectedUrls, scrappingId },
          }).unwrap();
          console.log("URLs trained successfully");
        } catch (urlError) {
          console.error("URL training failed:", urlError);
          toast({
            title: "Warning",
            description: "Failed to train URLs, continuing with files.",
            variant: "destructive",
          });
        }
      }

      if (hasFiles) {
        setTrainingStatus("Processing PDF documents...");
        try {
          await uploadTrainingFiles({
            voiceAgentId,
            files: uploadedFiles,
          }).unwrap();
          console.log("Files trained successfully");
        } catch (fileError) {
          console.error("File training failed:", fileError);
          toast({
            title: "Warning",
            description: "Failed to train files.",
            variant: "destructive",
          });
        }
      }

      setTrainingProgress(100); // Set to 100% on success
      toast({
        title: "Success",
        description: "Training initiated successfully!",
      });
      setTrainingStatus("Training in progress. This may take a few minutes.");
      setTimeout(() => {
        onNextStep();
      }, 3000);
    } catch (err) {
      console.error("Unexpected error during training:", err);
      toast({
        title: "Error",
        description: "An unexpected error occurred during training.",
        variant: "destructive",
      });
      success = false;
    } finally {
      setIsTraining(false);
      if (!success) {
        setTrainingStatus("");
        setTrainingProgress(0); // Reset on failure
      }
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  };

  const isLoading =
    isScanningUrl ||
    isTrainingUrls ||
    isUploadingFiles ||
    isTraining ||
    isScraping;

  return (
    <Card className="w-full border-none shadow-none">
      <CardHeader>
        <CardTitle className="text-center text-3xl font-bold">
          Select Training Sources for {agentName || "Your Agent"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-center text-gray-600 mb-8">
          Choose the sources you&apos;d like to use to train your voice agent. You
          can add website URLs or upload PDFs.
        </p>

        {showSummary && (
          <TrainingSummaryCard
            urlCount={selectedUrls.length}
            fileCount={uploadedFiles.length}
          />
        )}

        {scannedUrls.length === 0 && uploadedFiles.length === 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto mb-8">
            <Dialog open={isUrlDialogOpen} onOpenChange={setIsUrlDialogOpen}>
              <DialogTrigger asChild>
                <div className="border rounded-lg p-6 flex flex-col items-center hover:border-[#E74F13] hover:bg-[#E74F13]/5 cursor-pointer transition-all duration-200 transform hover:scale-[1.02]">
                  <div className="w-12 h-12 bg-[#E74F13] rounded-full flex items-center justify-center mb-4">
                    <Globe className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Website URL</h3>
                  <p className="text-gray-500 text-center text-sm">
                    Train your agent with content from your company website
                  </p>
                </div>
              </DialogTrigger>
              <URLDialog
                onURLSubmit={handleScanUrl}
                onScrapedURLsSubmit={handleScrapedURLsSubmit}
                isLoading={isScanningUrl || isScraping}
                scrapeProgress={scrapeProgress}
                streamStatus={streamStatus}
                onCancel={handleCancelScan}
                onOpen={handleDialogOpen}
              />
            </Dialog>

            <PDFDialog onClose={() => setIsPdfDialogOpen(false)} />
          </div>
        ) : null}

        {scannedUrls.length > 0 && (
          <div className="max-w-2xl mx-auto mb-8">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <Globe className="h-5 w-5 text-[#E74F13] mr-2" />
                <h3 className="text-lg font-medium">
                  Website URLs ({selectedUrls.length}/{scannedUrls.length}{" "}
                  selected)
                </h3>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedUrls([])}
                  disabled={selectedUrls.length === 0}
                  className="text-xs"
                >
                  Clear All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedUrls([...scannedUrls])}
                  disabled={selectedUrls.length === scannedUrls.length}
                  className="text-xs"
                >
                  Select All
                </Button>
              </div>
            </div>

            <div className="space-y-3 max-h-80 overflow-y-auto pr-2 rounded-lg border border-gray-100 p-2 bg-gray-50">
              {scannedUrls.map((url, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    selectedUrls.includes(url)
                      ? "border-[#E74F13] bg-white shadow-sm"
                      : "border-gray-200 bg-white"
                  } transition-colors duration-200`}
                >
                  <div className="flex items-center gap-3 overflow-hidden">
                    <Link
                      className={`shrink-0 ${
                        selectedUrls.includes(url)
                          ? "text-[#E74F13]"
                          : "text-gray-400"
                      }`}
                    />
                    <div className="truncate text-sm">
                      <a
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline flex items-center gap-1"
                      >
                        {url}
                        <ExternalLink size={12} />
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`h-8 w-8 rounded-full ${
                        selectedUrls.includes(url)
                          ? "text-[#E74F13]"
                          : "text-gray-400"
                      }`}
                      onClick={() => {
                        if (selectedUrls.includes(url)) {
                          handleRemoveUrl(url);
                        } else {
                          setSelectedUrls([...selectedUrls, url]);
                        }
                      }}
                    >
                      {selectedUrls.includes(url) ? (
                        <Check size={16} />
                      ) : (
                        <span className="w-4 h-4 rounded-full border border-gray-400"></span>
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full text-gray-400 hover:text-red-500"
                      onClick={() => handleRemoveFromScan(url)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {uploadedFiles.length > 0 && (
          <div className="max-w-2xl mx-auto mb-8">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <FileText className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="text-lg font-medium">
                  PDF Documents ({uploadedFiles.length})
                </h3>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  uploadedFiles.forEach(() => removeUploadedFile(0))
                }
                disabled={uploadedFiles.length === 0}
                className="text-xs"
              >
                Clear All
              </Button>
            </div>

            <div className="space-y-3 max-h-80 overflow-y-auto pr-2 rounded-lg border border-gray-100 p-2 bg-gray-50">
              {uploadedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg border border-blue-200 bg-white shadow-sm"
                >
                  <div className="flex items-center gap-3 overflow-hidden">
                    <FileText className="shrink-0 text-blue-600" />
                    <div className="truncate text-sm">
                      <p className="font-medium truncate">{file.name}</p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-full text-gray-400 hover:text-red-500"
                    onClick={() => handleRemovePdf(index)}
                  >
                    <Trash2 size={16} />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {(scannedUrls.length > 0 || uploadedFiles.length > 0) && (
          <div className="max-w-2xl mx-auto mb-8">
            <h3 className="text-lg font-medium mb-4">
              Add More Training Sources
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <Dialog open={isUrlDialogOpen} onOpenChange={setIsUrlDialogOpen}>
                <DialogTrigger asChild>
                  <div className="border rounded-lg p-6 flex flex-col items-center hover:border-[#E74F13] hover:bg-[#E74F13]/5 cursor-pointer transition-all duration-200 transform hover:scale-[1.02]">
                    <div className="w-12 h-12 bg-[#E74F13] rounded-full flex items-center justify-center mb-4">
                      <Globe className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Website URL</h3>
                    <p className="text-gray-500 text-center text-sm">
                      Add more website content to train your agent
                    </p>
                  </div>
                </DialogTrigger>
                <URLDialog
                  onURLSubmit={handleScanUrl}
                  onScrapedURLsSubmit={handleScrapedURLsSubmit}
                  isLoading={isScanningUrl || isScraping}
                  scrapeProgress={scrapeProgress}
                  streamStatus={streamStatus}
                  onCancel={handleCancelScan}
                  onOpen={handleDialogOpen}
                />
              </Dialog>
              <Dialog open={isPdfDialogOpen} onOpenChange={setIsPdfDialogOpen}>
                <PDFDialog onClose={() => setIsPdfDialogOpen(false)} />
              </Dialog>
            </div>
          </div>
        )}

        {trainingStatus && (
          <div className="max-w-2xl mx-auto mb-8 p-4 bg-gray-50 rounded-lg border border-gray-100 shadow-sm">
            <div className="flex items-center gap-2">
              {isTraining ? (
                <Loader2 className="h-5 w-5 animate-spin text-[#E74F13]" />
              ) : (
                <Check className="h-5 w-5 text-green-500" />
              )}
              <span className="font-medium">{trainingStatus}</span>
            </div>
            {isTraining && (
              <div className="mt-3">
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-[#E74F13] transition-all duration-300 ease-in-out"
                    style={{ width: `${trainingProgress}%` }}
                  ></div>
                </div>
                <div className="text-sm text-gray-500 mt-1 text-right">
                  {trainingProgress}%
                </div>
              </div>
            )}
          </div>
        )}

        <div className="flex justify-center gap-4 mt-8">
          <Button
            variant="outline"
            className="w-32 h-12 rounded-lg"
            onClick={() => window.history.back()}
            disabled={isLoading}
          >
            Back
          </Button>
          <Button
            className="w-32 bg-[#E74F13] hover:bg-[#E74F13]/90 h-12 rounded-lg transition-all duration-200 transform hover:scale-[1.02]"
            onClick={handleStartTraining}
            disabled={
              isLoading ||
              (selectedUrls.length === 0 && uploadedFiles.length === 0) ||
              isScanningUrl ||
              isScraping
            }
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Processing...
              </div>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default StepThree;
