import { useAuthStore } from "@/store/features/useAuthStore";
import useTrainingStore from "./useTrainingStore";

// Create a module scope variable for the abort controller
let currentAbortController: AbortController | null = null;

/**
 * Scans a URL and processes the streaming response
 */
export async function scanUrl(url: string): Promise<string> {
  const store = useTrainingStore.getState();

  // Reset state
  store.setIsLoading(true);
  store.setError(null);
  store.setScannedUrls([]);
  store.setScrapeProgress(0);
  store.setStreamStatus("🔍 Initializing website scan...");
  store.setScrappingId("");

  // Create new abort controller for this request
  currentAbortController = new AbortController();

  let totalUrls = 0;
  let processedUrls = 0;
  let scrappingId = "";

  try {
    const session = useAuthStore.getState().token;

    if (!session) {
      throw new Error("No authentication token found");
    }

    store.setStreamStatus("🌐 Connecting to your website...");
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/scraping?url=${encodeURIComponent(
        url
      )}`,
      {
        headers: {
          Authorization: `Bearer ${session}`,
          Accept: "text/event-stream",
        },
        signal: currentAbortController.signal,
      }
    );

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Failed to read response");
    }

    store.setStreamStatus(
      "📡 Website connection established! Starting analysis..."
    );
    await new Promise((resolve) => setTimeout(resolve, 800));

    let buffer = "";
    const textDecoder = new TextDecoder();

    const getRandomScanMessage = () => {
      const messages = [
        "🔍 Analyzing page content...",
        "📝 Extracting valuable information...",
        "🔎 Discovering website structure...",
        "📊 Processing website data...",
        "🌐 Mapping website architecture...",
      ];
      return messages[Math.floor(Math.random() * messages.length)];
    };

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }

      buffer += textDecoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        if (trimmedLine.startsWith("event: ")) {
          const event = trimmedLine.substring(7);
          const nextLine = lines.find((l) => l.trim().startsWith("data: "));

          if (nextLine) {
            try {
              const data = JSON.parse(nextLine.trim().substring(6));

              switch (event.trim()) {
                case "init":
                  if (data.total) {
                    totalUrls = data.total;
                    store.setScrapeProgress(5);
                    store.setStreamStatus(
                      `🎯 Found ${totalUrls} pages to analyze!`
                    );
                  }
                  break;

                case "url_processed":
                  if (data.processed && data.results) {
                    processedUrls = data.processed;
                    const progress = Math.floor(
                      (processedUrls / totalUrls) * 100
                    );

                    const result = data.results[0];
                    if (result && result.link) {
                      store.addUrl(result.link);
                      store.setScrapeProgress(progress);
                      store.setStreamStatus(
                        processedUrls === totalUrls
                          ? "✨ Analysis complete! Processing results..."
                          : `${getRandomScanMessage()} (${processedUrls}/${totalUrls} pages)`
                      );
                    }
                  }
                  break;

                case "req_completed":
                  if (data.scrapping_id) {
                    scrappingId = data.scrapping_id;
                    store.setScrappingId(scrappingId);
                  }
                  store.setScrapeProgress(90);
                  store.setStreamStatus(
                    "🎉 Almost there! Finalizing analysis..."
                  );
                  break;

                case "complete":
                  store.setIsLoading(false);
                  store.setScrapeProgress(100);
                  store.setStreamStatus(
                    "✅ Website analysis complete! Ready for training."
                  );
                  break;
              }
            } catch (e) {
              console.error("Error parsing data:", e);
            }
          }
        }
      }
    }
  } catch (error: unknown) {
    if (error instanceof Error && error.name !== "AbortError") {
      const message =
        error instanceof Error ? error.message : "Failed to fetch links";
      store.setError(message);
      store.setIsLoading(false);
      store.setStreamStatus("❌ Error occurred during scan");
      throw error;
    }
  } finally {
    store.setIsLoading(false);
  }

  return scrappingId;
}

/**
 * Cancels an ongoing scan
 */
export function cancelScan(): void {
  if (currentAbortController) {
    currentAbortController.abort();
    currentAbortController = null;

    const store = useTrainingStore.getState();
    store.setIsLoading(false);
    store.setStreamStatus("Scan cancelled");
  }
}
