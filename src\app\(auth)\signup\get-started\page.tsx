"use client";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";

import { useToast } from "@/hooks/use-toast";

import PhoneInput, { isPossiblePhoneNumber } from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { useUpdateProfileMutation } from "@/store/features/authApi";
import { useAuthStore } from "@/store/features/useAuthStore";
import AuthLayout from "../../components/auth-layout";

interface ApiError {
  data?: {
    message?: string;
  };
  status?: number;
  message?: string;
}

export default function CompleteProfileForm() {
  const [formData, setFormData] = useState({
    company: "",
    phone: "",
    title: "",
  });

  const [phoneError, setPhoneError] = useState("");
  const [updateProfile, { isLoading }] = useUpdateProfileMutation();
  const { toast } = useToast();
  const router = useRouter();
  const user = useAuthStore((state) => state.user);
  const updateUserProfile = useAuthStore((state) => state.updateUserProfile);

  // Pre-fill form with existing user data
  useEffect(() => {
    if (user) {
      setFormData((prev) => ({
        ...prev,
        company: user.company || "",
        phone: user.phone || "",
        title: user.title || "",
      }));
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handlePhoneChange = (value: string | undefined) => {
    setFormData((prev) => ({ ...prev, phone: value || "" }));
    if (value) {
      if (!isPossiblePhoneNumber(value)) {
        setPhoneError("Please enter a valid phone number");
      } else {
        setPhoneError("");
      }
    } else {
      setPhoneError("");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.phone && !isPossiblePhoneNumber(formData.phone)) {
      setPhoneError("Please enter a valid phone number");
      return;
    }

    try {
      // No need to pass token - the API will get it from the Zustand store
      await updateProfile({
        company: formData.company,
        phone: formData.phone,
        title: formData.title,
      }).unwrap();

      // Update local state in Zustand store
      updateUserProfile({
        company: formData.company,
        phone: formData.phone,
        title: formData.title,
      });

      toast({
        description: "Profile completed successfully",
      });

      router.push("/signin");
    } catch (error: unknown) {
      console.error("Profile update error:", error);
      const apiError = error as ApiError;
      toast({
        description: apiError?.data?.message || "Failed to update profile",
        variant: "destructive",
      });
    }
  };

  return (
    <AuthLayout
      heading="Create account"
      subheading="Complete your profile to get started"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="company">Company Name</Label>
          <Input
            id="company"
            name="company"
            type="text"
            placeholder="Example Corporation"
            value={formData.company}
            onChange={handleChange}
            required
            className="h-12"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <div className="phone-input-wrapper">
            <PhoneInput
              international
              defaultCountry="US"
              value={formData.phone}
              onChange={handlePhoneChange}
              className="h-12"
            />
            {phoneError && (
              <p className="text-sm text-red-500 mt-1">{phoneError}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="title">Title/Profession</Label>
          <Input
            id="title"
            name="title"
            type="text"
            placeholder="Enter your title"
            value={formData.title}
            onChange={handleChange}
            required
            className="h-12"
          />
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-[#E84B1C] hover:bg-[#E84B1C]/90 text-white mt-4"
          disabled={isLoading || !!phoneError}
        >
          {isLoading ? "Saving..." : "Get Started"}
        </Button>
      </form>

      <style jsx global>{`
        .phone-input-wrapper .PhoneInput {
          display: flex;
          align-items: center;
          background-color: white;
          padding: 0 0.75rem;
          border-radius: 0.5rem;
          border: 1px solid #e5e7eb;
          height: 48px;
        }

        .phone-input-wrapper .PhoneInputCountry {
          margin-right: 0.75rem;
          padding-right: 0.75rem;
          border-right: 1px solid #e5e7eb;
        }

        .phone-input-wrapper .PhoneInputInput {
          border: none;
          padding: 0;
          height: 46px;
          outline: none;
          font-size: 1rem;
          background: transparent;
        }

        .phone-input-wrapper .PhoneInputInput:focus {
          outline: none;
          box-shadow: none;
        }

        .phone-input-wrapper .PhoneInput--focus {
          border-color: #2c2c2c;
          box-shadow: 0 0 0 2px rgba(39, 39, 39, 0.2);
        }
      `}</style>
    </AuthLayout>
  );
}
