import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";

import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/store/providers";
import { SessionProvider } from "@/lib/session-provider";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${geistSans.variable} ${geistMono.variable} font-inter antialiased`}
      >
        <Providers>
          <SessionProvider>
            <Toaster /> {children}
          </SessionProvider>
        </Providers>
      </body>
    </html>
  );
}
