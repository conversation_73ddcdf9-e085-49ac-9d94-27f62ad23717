import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { supabase } from "@/lib/supabase";
import { useAuthStore } from "@/store/features/useAuthStore";

// Types
interface Business {
  id: string;
  business_name: string;
  business_phone: string;
  type_of_business: string;
  additional_context?: string;
  voiceAgentId: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateBusinessRequest {
  business_name: string;
  business_phone: string;
  type_of_business: string;
  additional_context?: string;
}

interface UpdateBusinessRequest {
  business_name?: string;
  business_phone?: string;
  type_of_business?: string;
  additional_context?: string;
}


interface RootState {
  auth?: {
    token: string | null;
  };
}

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Function to get token from Zustand store
const getZustandToken = (): string | null => {
  return useAuthStore.getState().token;
};

// Fallback function to get token from Supabase session
const getSupabaseSession = async (): Promise<string | null> => {
  const {
    data: { session },
    error,
  } = await supabase.auth.getSession();
  if (error) {
    console.error("Error getting session:", error.message);
    return null;
  }
  return session?.access_token ?? null;
};

export const businessApi = createApi({
  reducerPath: "businessApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: async (headers, { getState }) => {
      // First try to get token from Zustand store
      let token = getZustandToken();

      // If no token in Zustand, try Redux state
      if (!token) {
          token = (getState() as RootState).auth?.token || null;
      }

      // If still no token, try Supabase session as last resort
      if (!token) {
        token = await getSupabaseSession();
      }

      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      headers.set("Content-Type", "application/json");
      return headers;
    },
  }),
  endpoints: (builder) => ({
    // Create a new business linked to a voice agent
    createBusiness: builder.mutation<
      Business,
      { voiceAgentId: string; data: CreateBusinessRequest }
    >({
      query: ({ voiceAgentId, data }) => ({
        url: `/v1/business/${voiceAgentId}`,
        method: "POST",
        body: data,
      }),
    }),

    // Get all businesses linked to a voice agent
    getBusinessesByVoiceAgent: builder.query<Business[], string>({
      query: (voiceAgentId) => `/v1/business?voice_agent_id=${voiceAgentId}`,
    }),

    // Get a specific business by ID
    getBusiness: builder.query<Business, string>({
      query: (id) => `/v1/business/${id}`,
    }),

    // Update a business
    updateBusiness: builder.mutation<
      Business,
      { id: string; data: UpdateBusinessRequest }
    >({
      query: ({ id, data }) => ({
        url: `/v1/business/${id}`,
        method: "PATCH",
        body: data,
      }),
    }),

    // Delete a business
    deleteBusiness: builder.mutation<void, string>({
      query: (id) => ({
        url: `/v1/business/${id}`,
        method: "DELETE",
      }),
    }),
  }),

  // Enable cache invalidation for related queries
  tagTypes: ["Business"],
  refetchOnMountOrArgChange: true,
});

export const {
  useCreateBusinessMutation,
  useGetBusinessesByVoiceAgentQuery,
  useGetBusinessQuery,
  useUpdateBusinessMutation,
  useDeleteBusinessMutation,
} = businessApi;
