"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import AuthLayout from "../components/auth-layout";
import { useToast } from "@/hooks/use-toast";
import { useSignInMutation } from "@/store/features/authApi";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/features/useAuthStore";

interface ApiError {
  data?: {
    message?: string;
  };
  message?: string;
  status?: number;
}

export default function SignInForm() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [signIn, { isLoading }] = useSignInMutation();
  const setAuth = useAuthStore((state) => state.setAuth);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await signIn({ email, password }).unwrap();
      console.log(response);

      // Get the tokens from the response
      const token = response.access_token || response.token;
      const refreshToken = response.refresh_token || "";

      // Check if token exists
      if (!token) {
        throw new Error("No token received from the server");
      }

      // Parse JWT to get user data
      const parseJwt = (token: string) => {
        try {
          const base64Url = token.split(".")[1];
          const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
          const jsonPayload = decodeURIComponent(
            atob(base64)
              .split("")
              .map((c) => {
                return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
              })
              .join("")
          );
          return JSON.parse(jsonPayload);
        } catch (error) {
          console.error("Error parsing JWT:", error);
          return {};
        }
      };

      const decodedToken = parseJwt(token);

      // Create user object from token data
      const user = {
        id: decodedToken.sub || "",
        email: decodedToken.email || "",
        username: decodedToken.email?.split("@")[0] || "",

        name: decodedToken.user_metadata?.name || "",
      };

      setAuth(token, refreshToken, user);

      toast({
        description: "Successfully signed in",
      });

      router.push("/dashboard");
    } catch (error: unknown) {
      console.error("Sign in error:", error);
      const apiError = error as ApiError;
      toast({
        description:
          apiError?.data?.message ||
          apiError?.message ||
          "Authentication failed",
        variant: "destructive",
      });
    }
  };
  return (
    <AuthLayout
      heading="Log In"
      subheading="Welcome back! Please enter your details"
    >
      <form onSubmit={handleSubmit} className="space-y-4 ">
        {/* Google Sign In */}
        <button className="w-full flex items-center justify-center gap-2 p-3 border rounded-lg mb-6 bg-[#E9EAEA] hover:bg-gray-100 transition-colors">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 48 48"
            className="w-5 h-5"
          >
            <path
              fill="#FFC107"
              d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
            />
            <path
              fill="#FF3D00"
              d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
            />
            <path
              fill="#4CAF50"
              d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
            />
            <path
              fill="#1976D2"
              d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
            />
          </svg>
          <span className="font-normal">Continue with Google</span>
        </button>

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200"></div>
          </div>
          <div className="relative flex justify-center">
            <span className="bg-white px-4 text-sm text-gray-500">Or</span>
          </div>
        </div>
        <div className="space-y-2 ">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="h-12 "
          />
        </div>

        <div className="space-y-2 ">
          <div className="flex items-center justify-between">
            <Label htmlFor="password">Password</Label>
            <Link
              href="/reset-password"
              className="text-sm text-[#E84B1C] hover:text-[#E84B1C]/80"
            >
              Forgot?
            </Link>
          </div>
          <Input
            id="password"
            type="password"
            placeholder="Enter password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="h-12 "
          />
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-[#E84B1C] hover:bg-[#E84B1C]/90 text-white"
          disabled={isLoading}
        >
          {isLoading ? "Signing in..." : "Log in"}
        </Button>

        <p className="text-center text-gray-600 text-sm">
          Don&apos;t have an account yet?{" "}
          <Link
            href="/signup"
            className="text-[#E84B1C] hover:text-[#E84B1C]/80"
          >
            Sign up
          </Link>
        </p>
      </form>
    </AuthLayout>
  );
}
