"use client";
import { LucideIcon } from "lucide-react";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import Link from "next/link";
import Image, { StaticImageData } from "next/image";
import { cn } from "@/lib/utils";

interface IconRendererProps {
  icon: LucideIcon | string | StaticImageData;
  className?: string;
  isActive?: boolean;
}

const IconRenderer = ({ icon, className, isActive }: IconRendererProps) => {
  const combinedClassName = `${className} ${
    isActive ? "text-white " : "text-foreground"
  }`;

  // Handle the case when icon is undefined or invalid
  if (!icon) {
    return null;
  }

  // If icon is a string path
  if (typeof icon === "string") {
    return (
      <Image
        src={icon}
        alt="icon"
        width={16}
        height={16}
        className={`${combinedClassName} dark:invert`}
      />
    );
  }

  // If icon is an imported image object (like dashboardIcon)
  if (icon && typeof icon === "object" && "src" in icon) {
    return (
      <Image
        src={icon}
        alt="icon"
        width={16}
        height={16}
        className={`${combinedClassName} ${isActive && "invert"} dark:invert`}
      />
    );
  }

  // If icon is a Lucide component
  const IconComponent = icon as LucideIcon;
  return <IconComponent className={combinedClassName} />;
};

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon | StaticImageData | string;
    isActive?: boolean;
  }[];
}) {
  // Add a CSS class for active items that will override hover styles
  const activeItemClass =
    "bg-primary text-white p-6 mb-2 hover:bg-primary hover:text-white active:bg-primary focus:bg-primary focus-visible:bg-primary shadow-2xl";
  const inactiveItemClass =
    "hover:bg-accent/50 transition-all mb-2 duration-200 ease-in-out p-6";

  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton
              asChild
              className={cn(
                "flex items-center gap-3 py-2 px-3 rounded-xl shadow-none",
                item.isActive ? activeItemClass : inactiveItemClass
              )}
            >
              <Link href={item.url}>
                {item.icon && (
                  <IconRenderer
                    icon={item.icon}
                    className="h-4 w-4"
                    isActive={item.isActive}
                  />
                )}
                <span className={item.isActive ? "font-medium" : ""}>
                  {item.title}
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
