"use client";
import React, { useState } from "react";
import { ChevronDown, Bell, Search, Check } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { NavUser } from "./nav-user";
import { VoiceAgentSelector } from "./voice-agent-selector"; // Import the new component
import { SidebarTrigger } from "./ui/sidebar";
import { Separator } from "./ui/separator";

export function DashboardHeader() {
  // Get current date in the proper format
  const today = new Date();
  const options: Intl.DateTimeFormatOptions = {
    weekday: "long",
    month: "long",
    day: "numeric",
    year: "numeric",
  };
  const formattedDate = today.toLocaleDateString("en-US", options);

  // Language selection state with flags
  const [selectedLanguage, setSelectedLanguage] = useState({
    code: "US",
    name: "English",
    flag: "🇺🇸",
  });
  const languages = [
    { code: "US", name: "English", flag: "🇺🇸" },
    { code: "FR", name: "French", flag: "🇫🇷" },
    { code: "ES", name: "Spanish", flag: "🇪🇸" },
    { code: "DE", name: "German", flag: "🇩🇪" },
  ];

  // Search functionality
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <div className="flex justify-between items-center w-full py-5 px-6 mt-8 ">
      <div className="flex items-center gap-2 px-4 sm:hidden">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
      </div>
      {/* Left side - Dashboard title and date */}
      <div className="flex flex-col">
        <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
        <p className="text-sm text-gray-500">{formattedDate}</p>
      </div>

      {/* Middle section - Voice Agent selector and search */}
      <div className="flex items-center gap-4 flex-1 max-w-2xl mx-6">
        {/* Voice Agent selector - replaces the company selector */}
        <div className="w-[50%]">
          <VoiceAgentSelector />
        </div>

        {/* Search bar */}
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-primary" />
          </div>
          <Input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search here..."
            className="pl-10 pr-4 py-2 w-full border rounded-full h-12"
          />
        </div>
      </div>

      {/* Right side - Language selector, notifications, and profile */}
      <div className="flex items-center gap-6">
        {/* Language selector with flag */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-1 px-1">
              <span className="text-lg mr-1">{selectedLanguage.flag}</span>
              <ChevronDown className="h-3 w-3 text-gray-500" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {languages.map((lang) => (
              <DropdownMenuItem
                key={lang.code}
                onClick={() => setSelectedLanguage(lang)}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-2">
                  <span className="text-lg">{lang.flag}</span>
                  <span>{lang.name}</span>
                </div>
                {selectedLanguage.code === lang.code && (
                  <Check className="h-4 w-4" />
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Notifications */}
        <div className="border rounded-full p-1 px-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" className="relative p-1">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"></span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="end">
              <div className="p-3 border-b">
                <h3 className="font-medium">Notifications</h3>
              </div>
              <div className="p-4 max-h-72 overflow-y-auto">
                <div className="flex flex-col gap-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 mt-1.5 rounded-full bg-orange-500"></div>
                    <div>
                      <p className="text-sm">New user registered</p>
                      <p className="text-xs text-gray-500">2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 mt-1.5 rounded-full bg-orange-500"></div>
                    <div>
                      <p className="text-sm">System update completed</p>
                      <p className="text-xs text-gray-500">5 hours ago</p>
                    </div>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Profile */}
        <NavUser />
      </div>
    </div>
  );
}
