import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import use1 from "../../../public/images/use1.png";
import guse1 from "../../../public/images/guse1.png";
import guse2 from "../../../public/images/guse2.png";
import guse3 from "../../../public/images/guse3.png";
import use2 from "../../../public/images/use2.png";
import use3 from "../../../public/images/use3.png";




const UseCasesSection = () => {
  return (
    <section id="use-cases" className="py-20 px-4 md:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center mb-12">
        <Button
          variant="default"
          className="bg-orange-500 text-white mb-8 uppercase"
        >
          USE CASES
        </Button>
        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-12">
          AI voice bots are versatile
          <br />
          tools that drive efficiency
        </h2>

        {/* Category Pills */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          <Button className="bg-orange-500 text-white px-8 py-6 rounded-[50px]">
            Health Care
          </Button>
          <Button
            variant="outline"
            className="px-8 py-6 rounded-[50px] shadow-[inset_4px_10px_13px_0px_rgba(255,255,255,0.31),inset_-18px_-28px_37px_0px_rgba(0,0,0,0.27)]"
          >
            Sports
          </Button>
          <Button
            variant="outline"
            className="px-8 py-6 rounded-[50px] shadow-[inset_4px_10px_13px_0px_rgba(255,255,255,0.31),inset_-18px_-28px_37px_0px_rgba(0,0,0,0.27)]"
          >
            Information Technology
          </Button>
          <Button
            variant="outline"
            className="px-8 py-6 rounded-[50px] shadow-[inset_4px_10px_13px_0px_rgba(255,255,255,0.31),inset_-18px_-28px_37px_0px_rgba(0,0,0,0.27)]"
          >
            Fashion
          </Button>
        </div>
      </div>

      {/* Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto ">
        {/* Health Care Card */}
        <div className="relative h-full">
          {/* Background Gradient Image */}
          <div className="absolute inset-0">
            <Image
              src={guse1}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[500px] object-cover"
              priority
            />
          </div>

          {/* Content Container - using relative position to create new stacking context */}
          <div className="relative z-10">
            {/* Main Image */}
            <div className="p-4">
              <div className="rounded-3xl overflow-hidden mb-6">
                <Image
                  src={use1}
                  alt="Health Care"
                  width={800}
                  height={600}
                  className="w-full h-[300px] object-cover"
                  priority
                />
              </div>

              {/* Text Content */}
              <div className="p-4 relative">
                <h3 className="text-2xl font-bold mb-4 relative">
                  Health Care
                </h3>
                <p className="text-gray-700 relative">
                  Voice bots provide educational information about diseases,
                  treatments, and preventive measures. They empower patients to
                  make informed decisions about their health and wellness.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Information Technology Card */}
        <div className="relative h-full">
          {/* Background Gradient Image */}
          <div className="absolute inset-0">
            <Image
              src={guse2}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-cover"
              priority
            />
          </div>

          {/* Content Container - using relative position to create new stacking context */}
          <div className="relative z-10">
            {/* Main Image */}
            <div className="p-4">
              <div className="rounded-3xl overflow-hidden mb-6">
                <Image
                  src={use2}
                  alt="Health Care"
                  width={800}
                  height={600}
                  className="w-full h-[300px] object-cover"
                  priority
                />
              </div>

              {/* Text Content */}
              <div className="p-4 relative">
                <h3 className="text-2xl font-bold mb-4 relative">
                  Information Technology
                </h3>
                <p className="text-gray-700 relative">
                  In the fast-paced world of IT, timely and efficient support is
                  critical. AI voice bots transform IT service management by
                  automating routine tasks, providing instant assistance.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Sports Card */}

        <div className="relative h-full ">
          {/* Background Gradient Image */}
          <div className="absolute bottom-0 right-0">
            <Image
              src={guse3}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-cover"
              priority
            />
          </div>

          {/* Content Container - using relative position to create new stacking context */}
          <div className="relative pb-8 z-10">
            {/* Main Image */}
            <div className="p-4">
              <div className="rounded-3xl overflow-hidden mb-6">
                <Image
                  src={use3}
                  alt="Health Care"
                  width={800}
                  height={600}
                  className="w-full h-[300px] object-cover"
                  priority
                />
              </div>

              {/* Text Content */}
              <div className="p-4  relative">
                <h3 className="text-2xl font-bold mb-4 relative">Sports</h3>
                <p className="text-gray-700 relative">
                  AI voice bots are transforming the sports industry by
                  enhancing fan engagement, streamlining operations, and
                  delivering personalized experiences
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UseCasesSection;