import Image, { StaticImageData } from "next/image";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";


import twitterIconImg from "../../../../public/images/x-icon.png";
import whatsappIconImg from "../../../../public/images/whatsapp-icon.png";
import websiteIconImg from "../../../../public/images/web-icon.png";
import instagramIconImg from "../../../../public/images/insta-icon.png";




interface Platform {
  name: string;
  icon: StaticImageData;
  progress: number;
}

export default function CustomerGrowth() {
  const platforms: Platform[] = [
    {
      name: "X",
      icon: twitterIconImg,
      progress: 65,
    },
    {
      name: "WhatsApp",
      icon: whatsappIconImg,
      progress: 45,
    },
    {
      name: "Website",
      icon: websiteIconImg,
      progress: 85,
    },
    {
      name: "Instagram",
      icon: instagramIconImg,
      progress: 55,
    },
  ];

  return (
    <Card className="w-full ">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-7">
        <div>
          <CardTitle className="text-xl font-semibold text-gray-800">
            Customer Growth
          </CardTitle>
          <p className="text-sm text-gray-500">Track Your Customer Growth</p>
        </div>
        <Select defaultValue="today">
          <SelectTrigger className="w-[100px] text-sm h-10  rounded-full bg-[#E8E8E8]">
            <SelectValue placeholder="Today" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="yesterday">Yesterday</SelectItem>
            <SelectItem value="lastWeek">Last Week</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="flex gap-8">
        <div className="relative h-32 w-32 mt-4">
          {/* Overlapping circles */}
          <div className="absolute left-0 top-0 flex h-20 w-20 z-10 items-center justify-center rounded-full bg-[#FF6D33] text-white">
            <span className="text-lg font-semibold">2.87</span>
          </div>
          <div className="absolute right-[-1rem] top-[-1rem] flex h-24 w-24 items-center justify-center rounded-full bg-[#E74F13] text-white">
            <span className="text-lg font-semibold">2.87</span>
          </div>
          <div className="absolute bottom-[-1rem] left-[-0.5rem] flex h-20 w-20 z-20 items-center justify-center rounded-full bg-[#9A350D] text-white">
            <span className="text-lg font-semibold">2.87</span>
          </div>
          <div className="absolute bottom-0 right-0 flex h-20 w-20 z-50 items-center justify-center rounded-full bg-[#FF8D61] text-white">
            <span className="text-lg font-semibold">2.87</span>
          </div>
        </div>

        <div className="flex flex-1 flex-col gap-4">
          {platforms.map((platform) => (
            <div key={platform.name} className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                <Image
                  src={platform.icon}
                  alt={`${platform.name} icon`}
                  width={20}
                  height={20}
                  className="h-5 w-5"
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-700">
                    {platform.name}
                  </span>
                </div>
                <div className="mt-1 h-1.5 w-full overflow-hidden rounded-full bg-gray-100">
                  <div
                    className="h-full rounded-full bg-[#E74F13]"
                    style={{ width: `${platform.progress}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
