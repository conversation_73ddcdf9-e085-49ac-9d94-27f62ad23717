"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import bg from "../../public/images/background-img1.png";
import desktop from "../../public/images/Desktop.png";
import textBg from "../../public/images/textbg.png";
import Navigation from "@/components/landing-page/navigation";
import UseCasesSection from "@/components/landing-page/use-cases";
import ExperienceSection from "@/components/landing-page/experience-section";
import FeatureSection from "@/components/landing-page/feature-section";
import FAQSection from "@/components/landing-page/fqa-section";
import Footer from "@/components/landing-page/footer";

const HeroSection = () => {
  return (
    <div className=" min-h-screen bg-white overflow-hidden">
      {/* Navigation */}
      <Navigation />

      {/* Hero Content */}
      <section id="home" className="container mx-auto px-6 pt-20 pb-32">
        <div className="relative z-10 text-center max-w-4xl mx-auto">
          <h1 className="text-6xl font-bold mb-6 tracking-tight">
            <span className="relative">
              THE{" "}
              <Button
                className=" absolute bottom-[-4rem] left-[-1rem]  hidden lg:flex
                 bg-orange-600 hover:bg-orange-700
                 text-white font-medium
                px-6
                 transition-colors duration-200
                 rounded rounded-r-none"
              >
                Join to us!
              </Button>
            </span>
            <span className=" px-4 relative">
              {" "}
              <Image
                src={textBg}
                className="absolute top-[-4px] right-[-4px]"
                alt={""}
              />
              NEW ERA
            </span>
            OF VOICE{" "}
            <span className="  relative">
              {" "}
              <Button
                variant="outline"
                className=" absolute bottom-[-1rem] right-[-6rem] hidden lg:flex border border-[#E74F13]"
              >
                INTEGRATED AI
              </Button>{" "}
              BOT{" "}
            </span>
          </h1>

          <p className=" text-[#60626B] font-bold text-center text-[16px] mb-12 ">
            EMPOWERING CREATORS WITH{" "}
            <span className="block">POWERFUL TOOLS</span>
          </p>

          <div className="flex items-center justify-center space-x-4">
            <Button
              variant="outline"
              className="bg-gradient-to-r text-white from-[#E74F13] via-[#E74F13]/76 to-[#E74F13] border border-[#EAF1FF] shadow-[inset_4px_10px_13px_0px_rgba(255,255,255,0.31)] px-10 py-6 rounded-[50px]"
            >
              Get Started
            </Button>
            <Button
              variant="outline"
              className="px-8 py-6 rounded-[50px] shadow-[inset_4px_10px_13px_0px_rgba(255,255,255,0.31),inset_-18px_-28px_37px_0px_rgba(0,0,0,0.27)]"
            >
              Request A Demo
            </Button>
          </div>
        </div>
        <div className="absolute w-full   lg:top-[18rem] xl:top-[14rem] 2xl:top-[10rem] left-0 right-0  mx-auto aspect-[16/9]">
          <Image src={bg} alt={"Operator-ai"} />
          <div className="relative">
            <div className="absolute w-full bottom-[-8rem] sm:bottom-[-20rem] xl:bottom-[-14rem]  2xl:bottom-[-8rem]  right-0 left-0 lg:max-w-7xl  mx-auto aspect-[16/9]">
              <Image src={desktop} alt="" />
            </div>
          </div>
        </div>
      </section>

      <FeatureSection />
      <UseCasesSection />
      <ExperienceSection />
      <FAQSection />
      <Footer />
    </div>
  );
};

export default HeroSection;
