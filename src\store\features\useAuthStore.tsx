import { create } from "zustand";
import { persist } from "zustand/middleware";
import { supabase } from "@/lib/supabase";

interface User {
  id: string;
  email: string;
  username: string;
  company?: string;
  phone?: string;
  title?: string;
  name?: string;
  avatar?: string;
}

interface AuthState {
  token: string | null;
  refreshToken: string | null;
  user: User | null;
  setAuth: (token: string, refreshToken: string, user: User) => void;
  clearAuth: () => void;
  updateUserProfile: (userData: Partial<User>) => void;
  refreshSession: () => Promise<boolean>;
  logout: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      token: null,
      refreshToken: null,
      user: null,
      setAuth: (token, refreshToken, user) =>
        set({ token, refreshToken, user }),
      clearAuth: () => set({ token: null, refreshToken: null, user: null }),
      updateUserProfile: (userData) =>
        set((state) => ({
          user: state.user ? { ...state.user, ...userData } : null,
        })),
      refreshSession: async () => {
        try {
          const refreshToken = get().refreshToken;
          if (!refreshToken) return false;

          // Use Supabase's built-in refresh method
          const { data, error } = await supabase.auth.refreshSession({
            refresh_token: refreshToken,
          });

          if (error || !data.session) {
            console.error("Failed to refresh session:", error);
            return false;
          }

          // Update the store with new tokens
          const { access_token, refresh_token } = data.session;
          const userData = get().user;

          if (access_token && refresh_token && userData) {
            set({
              token: access_token,
              refreshToken: refresh_token,
            });
            return true;
          }
          return false;
        } catch (error) {
          console.error("Error refreshing session:", error);
          return false;
        }
      },
      logout: async () => {
        try {
          // Clear Supabase session if it exists
          await supabase.auth.signOut();
          // Clear store
          set({ token: null, refreshToken: null, user: null });
        } catch (error) {
          console.error("Error during logout:", error);
          // Still clear local state even if API call fails
          set({ token: null, refreshToken: null, user: null });
        }
      },
    }),
    {
      name: "auth-storage",
      // Persist token, refresh token and user data
      partialize: (state) => ({
        token: state.token,
        refreshToken: state.refreshToken,
        user: state.user,
      }),
    }
  )
);
