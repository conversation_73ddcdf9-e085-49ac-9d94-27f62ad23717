"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import blurLogo from "../../../../../public/images/sources.png";
import Image from "next/image";

export function EmptySourcesState({
  onAddNewSource,
}: {
  onAddNewSource: () => void;
}) {
  return (
    <div className="w-full h-full md:mt-60  flex flex-col ">
      <div className="flex-1 flex items-center justify-center">
        <div className="max-w-2xl w-full border border-primary rounded-lg p-4 text-center mx-auto">
          <div className="border-2 border-dashed p-12 border-[#E8E8E8] rounded-xl">
            <div className="flex justify-center mb-4">
              <Image
                src={blurLogo}
                alt="empty-sources-logo"
                width={40}
                height={40}
              />
            </div>
            <h2 className="text-xl font-semibold mb-2">Add New Sources</h2>
            <p className="text-gray-600 mb-6">
              Train a new bot with your own documentation and content.
            </p>
            <Button
              onClick={onAddNewSource}
              className="bg-white text-primary border border-orange-500 hover:bg-orange-50"
            >
              Add New Source
            </Button>
          </div>
        </div>
      </div>
      
    </div>
  );
}
