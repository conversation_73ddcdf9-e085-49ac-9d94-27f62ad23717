
import { Card } from "@/components/ui/card";
import Image from "next/image";
import SalesIcon from "../../../../public/images/sales.png";
import CustomerIcon from "../../../../public/images/custumers-Icon.png";
import OrderIcon from "../../../../public/images/Union.png";

export default function MetricCards() {
  const metrics = [
    {
      title: "Total Sales",
      value: "$1k",
      change: "+8%",
      timeframe: "from yesterday",
      percentage: "+2.00%",
      icon: <Image src={SalesIcon} alt="Sales Icon" width={30} height={30} />,
      variant: "orange",
    },
    {
      title: "New Customers",
      value: "$1k",
      change: "+5%",
      timeframe: "from yesterday",
      percentage: "+2.00%",
      icon: (
        <Image
          src={OrderIcon}
          alt="Order Icon"
          width={20}
          height={20}
          className="text-black"
        />
      ),
      variant: "white",
    },
    {
      title: "Product Sold",
      value: "$1k",
      change: "+5%",
      timeframe: "from yesterday",
      percentage: "+2.00%",
      icon: (
        <Image src={CustomerIcon} alt="Customer Icon" width={30} height={30} />
      ),
      variant: "white",
    },
    {
      title: "Total Orders",
      value: "$1k",
      change: "+5%",
      timeframe: "from yesterday",
      percentage: "+2.00%",
      icon: (
        <Image src={CustomerIcon} alt="Customer Icon" width={30} height={30} />
      ),
      variant: "white",
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-1 lg:grid-cols-2">
      {metrics.map((metric, index) => (
        <Card
          key={index}
          className={`relative overflow-hidden p-6 ${
            metric.variant === "orange" ? "bg-[#E74F13]" : "bg-white"
          }`}
        >
          <div
            className={`absolute right-4 top-8 rounded-full px-2 py-1 text-xs font-medium ${
              metric.variant === "orange"
                ? "bg-[#8B2B07] text-white"
                : "bg-[#E74F13] text-white"
            }`}
          >
            {metric.percentage}
          </div>
          <div
            className={`mb-4 flex h-10 w-10 items-center justify-center rounded-xl ${
              metric.variant === "orange" ? "bg-white" : "bg-[#E8E8E8]"
            }`}
          >
            {metric.icon}
          </div>
          <div className="space-y-2 mt-8">
            <p
              className={`text-sm font-medium ${
                metric.variant === "orange" ? "text-white/80" : "text-gray-500"
              }`}
            >
              {metric.title}
            </p>
            <div className="flex gap-2 justify-start items-center">
              <p
                className={`text-2xl font-bold ${
                  metric.variant === "orange" ? "text-white" : "text-gray-900"
                }`}
              >
                {metric.value}
              </p>
              <div
                className={`text-sm ${
                  metric.variant === "orange"
                    ? "text-white/80"
                    : "text-gray-500"
                }`}
              >
                <span className="font-medium">{metric.change}</span>{" "}
                {metric.timeframe}
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
