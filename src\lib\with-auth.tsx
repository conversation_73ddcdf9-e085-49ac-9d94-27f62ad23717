import { useEffect } from "react";
import { useRouter } from "next/router";
import { useAuthStore } from "@/store/features/useAuthStore";

export const withAuth = <P extends object>(
  WrappedComponent: React.ComponentType<P>
) => {
  const Wrapper: React.FC<P> = (props) => {
    const router = useRouter();
    const { token } = useAuthStore();

    useEffect(() => {
      if (!token) {
        router.push("/signin"); // Redirect to login page if not authenticated
      }
    }, [token, router]);

    // If there's a token, render the wrapped component
    return token ? <WrappedComponent {...props} /> : null;
  };

  return Wrapper;
};
