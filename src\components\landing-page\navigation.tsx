import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Menu, User, Settings, LogOut } from "lucide-react";
import logo from "../../../public/images/verbal.webp";
import { useAuthStore } from "@/store/features/useAuthStore";
import { useGetCustomerDataQuery } from "@/store/features/authApi";
import { useRouter } from "next/navigation";

interface NavLinkProps {
  href: string;
  current: boolean;
  children: React.ReactNode;
}

const navigation = [
  { name: "Home", href: "#home", current: true },
  { name: "Features", href: "#features", current: false },
  { name: "Use Cases", href: "#use-cases", current: false },
  { name: "About us", href: "#about", current: false },
  { name: "FAQ", href: "#faq", current: false },
];

const NavLink = ({ href, current, children }: NavLinkProps) => {
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const targetId = href.replace("#", "");
    const element = document.getElementById(targetId);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={`${
        current
          ? "text-orange-500"
          : "text-gray-600 hover:text-orange-500 transition-colors"
      }`}
    >
      {children}
    </Link>
  );
};

interface MobileNavLinkProps extends NavLinkProps {
  onClick?: () => void;
}

const MobileNavLink = ({
  href,
  current,
  children,
  onClick,
}: MobileNavLinkProps) => {
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const targetId = href.replace("#", "");
    const element = document.getElementById(targetId);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
    onClick?.(); // Close mobile menu
  };

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={`${
        current
          ? "text-orange-500"
          : "text-gray-600 hover:text-orange-500 transition-colors"
      } block py-2`}
    >
      {children}
    </Link>
  );
};

const UserMenu = () => {
  const { user, logout } = useAuthStore();
  const router = useRouter();

  const { data: customerData } = useGetCustomerDataQuery();

  console.log(customerData);

  const handleLogout = async () => {
    await logout();
    router.push("/signin");
  };

  const initials = user?.username
    ? `${customerData?.username.charAt(0).toUpperCase()}`
    : customerData?.email?.charAt(0).toUpperCase() || "?";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full">
          <Avatar>
            <AvatarImage
              src=""
              alt={customerData?.username || customerData?.email || "User"}
            />
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium">
              {customerData?.username || "User"}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {customerData?.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Sign out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const Navigation = () => {
  const [open, setOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const { token, user, logout } = useAuthStore();
  const isLoggedIn = !!token && !!user;
  const router = useRouter();

  useEffect(() => {
    const handleScroll = () => {
      const sections = ["home", "features", "use-cases", "about", "faq"];
      const currentSection = sections.find((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top >= 0 && rect.top <= window.innerHeight / 2;
        }
        return false;
      });

      if (currentSection) {
        setActiveSection(currentSection);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleMobileLogout = async () => {
    setOpen(false);
    await logout();
    router.push("/signin");
  };

  return (
    <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-sm">
      <div className="flex items-center justify-between p-6">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <Image src={logo} alt="Logo" width={100} height={100} />
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          {navigation.map((item) => (
            <NavLink
              key={item.name}
              href={item.href}
              current={activeSection === item.href.replace("#", "")}
            >
              {item.name}
            </NavLink>
          ))}
        </div>

        {/* Desktop Auth Buttons or User Menu */}
        <div className="hidden md:flex items-center space-x-4">
          {isLoggedIn ? (
            <UserMenu />
          ) : (
            <>
              <Button variant="ghost" className="text-gray-600">
                <Link href="/signin">Sign in</Link>
              </Button>
              <Button
                className="bg-[#FFFFFF] 
      border border-[#E74F13]
      shadow-[inset_4px_10px_13px_0px_rgba(231,79,19,0.31),inset_-18px_-68px_37px_0px_rgba(231,79,19,0.27)] text-black hover:text-white"
              >
                <Link href="/signup">Sign up</Link>
              </Button>
            </>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>
                  <div className="flex items-center space-x-2">
                    <Image src={logo} alt="Logo" width={100} height={100} />
                  </div>
                </SheetTitle>
              </SheetHeader>
              <div className="flex flex-col space-y-4 mt-6">
                {navigation.map((item) => (
                  <MobileNavLink
                    key={item.name}
                    href={item.href}
                    current={activeSection === item.href.replace("#", "")}
                    onClick={() => setOpen(false)}
                  >
                    {item.name}
                  </MobileNavLink>
                ))}
                <div className="pt-4 space-y-4">
                  {isLoggedIn ? (
                    <>
                      <div className="flex items-center space-x-3 p-2">
                        <Avatar>
                          <AvatarFallback>
                            {user?.username?.charAt(0).toUpperCase() ||
                              user?.email?.charAt(0).toUpperCase() ||
                              "?"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">
                            {user?.username || "User"}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {user?.email}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => {
                          setOpen(false);
                          // Navigation to profile page
                        }}
                      >
                        <User className="mr-2 h-4 w-4" />
                        Profile
                      </Button>
                      <Button
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => {
                          setOpen(false);
                          // Navigation to settings page
                        }}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </Button>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-red-500"
                        onClick={handleMobileLogout}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        Sign out
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button variant="ghost" className="w-full text-gray-600">
                        <Link href="/signin" onClick={() => setOpen(false)}>
                          Sign in
                        </Link>
                      </Button>
                      <Button className="w-full bg-orange-100 text-orange-500 hover:bg-orange-200">
                        <Link href="/signup" onClick={() => setOpen(false)}>
                          Sign up
                        </Link>
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
