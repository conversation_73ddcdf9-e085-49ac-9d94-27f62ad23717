// store/index.ts
import { configureStore } from "@reduxjs/toolkit";
import { authApi } from "./features/authApi";
import { voiceAgentApi } from "./features/voiceAgentApi";
import { businessApi } from "./features/businessApi";
import { trainingApi } from "./features/trainingApi";
import { sourcesApi } from "./features/sourcesApi";

export const store = configureStore({
  reducer: {
    [authApi.reducerPath]: authApi.reducer,
    [voiceAgentApi.reducerPath]: voiceAgentApi.reducer,
    [businessApi.reducerPath]: businessApi.reducer,
    [trainingApi.reducerPath]: trainingApi.reducer,
    [sourcesApi.reducerPath]: sourcesApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      voiceAgentApi.middleware,
      businessApi.middleware,
      trainingApi.middleware,
      sourcesApi.middleware
    ),
});
