"use client";
import { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { useResetPasswordMutation } from "@/store/features/authApi";
import { useToast } from "@/hooks/use-toast";
import AuthLayout from "../../components/auth-layout";

interface ResetPasswordError {
  message?: string;
  data?: {
    message?: string;
  };
  status?: number;
}

// Create a separate component that uses searchParams
function ResetEmailContent() {
  const searchParams = useSearchParams();
  const email = searchParams.get("email");
  const [resetPassword, { isLoading }] = useResetPasswordMutation();
  const { toast } = useToast();

  const handleResend = async () => {
    if (!email) return;
    try {
      await resetPassword(email).unwrap();
      toast({
        description: "Reset password email resent successfully",
      });
    } catch (error: unknown) {
      // Cast the unknown error to our specific error type
      const resetError = error as ResetPasswordError;

      toast({
        description:
          resetError.message ||
          resetError.data?.message ||
          "Failed to resend reset email",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        We sent password reset link to your
        <br />
        {email}
      </p>
      <Button
        onClick={() => (window.location.href = "mailto:")}
        className="w-full h-12 bg-[#E84B1C] hover:bg-[#E84B1C]/90 text-white"
      >
        Open email app
      </Button>
      <div className="text-center">
        <p className="text-gray-600 inline">
          Didn&apos;t receive email?{" "}
          <button
            onClick={handleResend}
            disabled={isLoading}
            className="text-[#E84B1C] hover:text-[#E84B1C]/80 inline font-normal"
          >
            Click to resend
          </button>
        </p>
      </div>
    </div>
  );
}

// Loading fallback component
function LoadingState() {
  return (
    <div className="space-y-6">
      <p className="text-gray-600">Loading...</p>
    </div>
  );
}

// Main component with Suspense boundary
export default function ResetEmailSent() {
  return (
    <AuthLayout heading="Reset Password" subheading="">
      <Suspense fallback={<LoadingState />}>
        <ResetEmailContent />
      </Suspense>
    </AuthLayout>
  );
}
