"use client";

import React from "react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Radial<PERSON>hart } from "./radicle-chart";
import MetricCards from "./metrics-card";
import CustomerHabitsChart from "./bar-chart";
import CustomerGrowth from "./customer-growth";


// Visitor Insights Chart
const VisitorInsightsChart = () => {
  return (
    <div className="flex justify-center items-center ">
      <div className="relative w-[270px] h-[270px]">
        {/* Semi-circular chart - using border radius for arc segments */}
        <RadialChart />
      </div>
    </div>
  );
};

// Customer Growth Chart

export default function Dashboard() {
  return (
    <div className="p-6 bg-gray-50">
      {/* Main layout with 70/30 split */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Left section - 70% width */}
        <div className="lg:w-[70%] space-y-6">
          {/* Stats cards grid - 2x2 */}

          <MetricCards />

          {/* Customer Habits Bar Chart */}

          <CustomerHabitsChart />
        </div>

        {/* Right section - 30% width */}
        <div className="lg:w-[30%] space-y-6">
          {/* Visitor Insights */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-xl font-bold">
                  Visitor Insights
                </CardTitle>
                <CardDescription>Track Your Visitor Insights</CardDescription>
              </div>
              <Select defaultValue="today">
                <SelectTrigger className="w-[100px] text-sm h-10  rounded-full bg-[#E8E8E8]">
                  <SelectValue placeholder="Today" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="lastWeek">Last Week</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="flex flex-col">
              <VisitorInsightsChart />

              <div className="space-y-1 mt-0">
                <div className="flex w-full flex-col gap-3">
                  <div className="flex items-center gap-2">
                    <div className="h-2.5 w-2.5 rounded-full bg-[#E74F13]"></div>
                    <span className="text-sm text-gray-700">
                      Loyal Customers
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2.5 w-2.5 rounded-full bg-[#F76F39]"></div>
                    <span className="text-sm text-gray-700">New Customers</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2.5 w-2.5 rounded-full bg-[#8B2B07]"></div>
                    <span className="text-sm text-gray-700">
                      Unique Customers
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Growth */}
          <CustomerGrowth />
        </div>
      </div>
    </div>
  );
}
