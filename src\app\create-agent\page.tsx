"use client";

import React, { ReactNode } from "react";
import Logo from "./components/top-logo";
import StepOne from "./components/step-one";
import StepTwo from "./components/step-two";
import StepThree from "./components/step-three";
import StepFour from "./components/step-four";


const steps = [
  { title: "Agent Name", description: "Name" },
  { title: "Business Information", description: "information" },
  { title: "Training Sources", description: "Sources" },
  { title: "Assistant Testing", description: "Testing" },
];

interface LayoutProps {
  children: ReactNode;
}
const AgentCreationPage = () => {
  const [currentStep, setCurrentStep] = React.useState(0);

  const handleNext = (
    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<HTMLFormElement>
  ) => {
    e.preventDefault();
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  // const handleBack = (e: React.MouseEvent) => {
  //   e.preventDefault();
  //   setCurrentStep((prev) => Math.max(prev - 1, 0));
  // };

  // const handleFinish = () => {
  //   console.log("Agent creation completed!");
  //   // Add your finish logic here
  // };

  // Common layout components
  const Layout = ({ children }: LayoutProps) => (
    <div className="min-h-screen bg-white">
      <nav>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Logo />
        </div>
      </nav>
      <main className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="flex justify-center mb-12">
          <Logo width={100} height={100} />
        </div>
        {/* Stepper */}
        <div className="mb-12">
          <div className="flex justify-between items-center">
            {steps.map((step, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div className="flex items-center w-full">
                  <div
                    className={`w-full h-1 ${index === 0 ? "hidden" : ""} ${
                      index <= currentStep ? "bg-[#E74F13]" : "bg-gray-200"
                    }`}
                  ></div>
                  <div
                    className={`w-8 h-8 rounded-full flex items-center p-4 justify-center ${
                      index <= currentStep
                        ? "bg-[#E74F13] text-white"
                        : "bg-gray-200"
                    }`}
                  >
                    {index + 1}
                  </div>
                  <div
                    className={`w-full h-1 ${
                      index === steps.length - 1 ? "hidden" : ""
                    } ${index < currentStep ? "bg-[#E74F13]" : "bg-gray-200"}`}
                  ></div>
                </div>
                <div className="text-sm mt-2">
                  <div className="font-medium text-gray-900 text-center">
                    {step.title}
                  </div>
                  <div className="text-gray-500 text-center">
                    {step.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        {children}
      </main>
    </div>
  );

  return (
    <Layout>
      {currentStep === 0 && <StepOne handleNext={handleNext} />}
      {currentStep === 1 && <StepTwo onNextStep={() => setCurrentStep(2)} />}
      {currentStep === 2 && <StepThree onNextStep={() => setCurrentStep(3)} />}
      {currentStep === 3 && <StepFour />}
    </Layout>
  );
};

export default AgentCreationPage
