"use client";
import Image from "next/image";
import {
  Phone,
  Facebook,
  Twitter,
  Instagram,
  DribbbleIcon as <PERSON><PERSON><PERSON>,
  ArrowUp,
} from "lucide-react";
import globe from "../../../public/images/globe.png";
import Link from "next/link";
import logo from "../../../public/images/verbal.webp";
import { useEffect, useState } from "react";

export default function Footer() {
  const [showScroll, setShowScroll] = useState(false);

  useEffect(() => {
    const checkScrollTop = () => {
      if (window.scrollY > 300) {
        setShowScroll(true);
      } else {
        setShowScroll(false);
      }
    };

    window.addEventListener("scroll", checkScrollTop);
    return () => window.removeEventListener("scroll", checkScrollTop);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };
  return (
    <footer className="relative w-full bg-[#F5F5F5] px-4 py-16">
      <div className="mx-auto container">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
          {/* Left Section */}
          <div className="space-y-6">
            <h2 className="text-3xl font-bold">AI Operator</h2>
            <p className="text-gray-600 max-w-xs">
              A product that instantly converts your entire workforce into
              AI-enabled super employees
            </p>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="bg-[#E85C2C] rounded-full p-2">
                  <Phone className="h-4 w-4 text-white" />
                </div>
                <span className="text-gray-600">123 456 789</span>
              </div>
              <Link
                href="mailto:<EMAIL>"
                className="text-gray-600 hover:text-[#E85C2C] underline block"
              >
                <EMAIL>
              </Link>
            </div>
          </div>

          {/* Center Section */}
          <div className="relative w-full h-[400px] flex items-center justify-center">
            <Image
              src={globe}
              alt="AI Globe Visualization"
              width={400}
              height={400}
              className="object-contain"
            />
          </div>

          {/* Right Section */}
          <div className="flex justify-end items-end flex-col gap-12">
            {/* Social Links */}
            <div className="flex flex-col gap-6">
              <Link href="#" className="hover:text-[#E85C2C] transition-colors">
                <Facebook className="h-5 w-5" />
              </Link>
              <Link href="#" className="hover:text-[#E85C2C] transition-colors">
                <Twitter className="h-5 w-5" />
              </Link>
              <Link href="#" className="hover:text-[#E85C2C] transition-colors">
                <Instagram className="h-5 w-5" />
              </Link>
              <Link href="#" className="hover:text-[#E85C2C] transition-colors">
                <Behance className="h-5 w-5" />
              </Link>
            </div>

            {/* Vertical Text */}
            <div className="hidden md:block relative ">
              <span className="rotate-90 inline-block origin-left transform translate-y-[-100%] text-gray-600 whitespace-nowrap absolute right-[-76px] b">
                Get in touch
              </span>
            </div>
          </div>
        </div>

        <div className="flex justify-center fixed bottom-24 right-0 left-0">
          <button
            onClick={scrollToTop}
            className={`transform transition-all duration-300 ${
              showScroll
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-10"
            } border border-[#d64e1f] text-black p-4 rounded-full hover:bg-[#d64e1f] hover:text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2`}
            aria-label="Scroll to top"
          >
            <ArrowUp className="h-6 w-6" />
          </button>
        </div>

        {/* Bottom Logo */}
        <div className="mt-16 flex justify-center ">
          <div className="flex items-center gap-2">
            <Image src={logo} alt="Logo" width={100} height={100} />
          </div>
        </div>
      </div>
    </footer>
  );
}
