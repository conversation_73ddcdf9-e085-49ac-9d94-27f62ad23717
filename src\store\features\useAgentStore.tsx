import { create } from "zustand";
import { persist } from "zustand/middleware";

// Define the store state interface
interface AgentState {
  voiceAgentId: string | null;
  selectedBusinessId: string;
  voice: string;
  agentName: string;
  isLoading: boolean;
  error: string | null;
  setVoiceAgentId: (id: string) => void;
  setVoice: (voice: string) => void;
  setSelectedBusinessId: (id: string) => void;
  setAgentName: (name: string) => void;
  resetStore: () => void;
  setLoading: (status: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

// Create agent store with persistence
const useAgentStore = create<AgentState>()(
  persist(
    (set) => ({
      voiceAgentId: null, 
      selectedBusinessId: "",
      voice: "alloy",
      agentName: "",
      isLoading: false,
      error: null,
      // Set the voice agent ID
      setVoiceAgentId: (id: string) => set({ voiceAgentId: id }),
      // Set the selected business ID
      setSelectedBusinessId: (id: string) => set({ selectedBusinessId: id }),
      setAgentName: (name: string) => set({ agentName: name }),
      setVoice: (voice) => set({ voice }),
      // Reset store when needed
      resetStore: () =>
        set({
          voiceAgentId: null, // Reset to null
          selectedBusinessId: "",
          agentName: "",
          error: null,
        }),
      // Set loading state
      setLoading: (status: boolean) => set({ isLoading: status }),
      // Set error state
      setError: (error: string | null) => set({ error }),
      // Clear error
      clearError: () => set({ error: null }),
    }),
    {
      name: "agent-storage", // name of the item in localStorage
      partialize: (state) => ({
        voiceAgentId: state.voiceAgentId,
        selectedBusinessId: state.selectedBusinessId,
        agentName: state.agentName,
      }),
    }
  )
);

export default useAgentStore;
