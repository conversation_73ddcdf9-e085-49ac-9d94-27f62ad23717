"use client";
import React, { useEffect, useState } from "react";
import {
  ChevronDown,
  Check,
  Loader2,
  Trash2,
  AlertCircle,
  Plus,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  useGetVoiceAgentsQuery,
  useDeleteVoiceAgentMutation,
} from "@/store/features/voiceAgentApi";
import useAgentStore from "@/store/features/useAgentStore";
import Link from "next/link";
import { useRouter } from "next/navigation";

// Define types for better TypeScript support
interface AgentWithVisuals {
  id: string;
  name: string;
  icon: string;
  color: string;
  systemContext?: string;
}

// Generate color and get initials functions remain the same
const generateColor = (name: string): string => {
  const colors = [
    "bg-blue-500",
    "bg-green-500",
    "bg-orange-500",
    "bg-purple-500",
    "bg-red-500",
    "bg-indigo-500",
    "bg-pink-500",
    "bg-yellow-500",
    "bg-teal-500",
  ];

  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }

  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

const getInitials = (name: string): string => {
  return (
    name
      .split(" ")
      .map((word) => (word.length > 0 ? word[0] : ""))
      .join("")
      .toUpperCase()
      .substring(0, 2) || "VA"
  );
};

export function VoiceAgentSelector(): JSX.Element {
  const router = useRouter();

  // Fetch voice agents using RTK Query
  const {
    data: voiceAgents,
    isLoading,
    error,
    refetch,
  } = useGetVoiceAgentsQuery(undefined, {
    refetchOnMountOrArgChange: false,
  });

  // Delete agent mutation
  const [deleteVoiceAgent, { isLoading: isDeleting }] =
    useDeleteVoiceAgentMutation();

  // Get and set the selected agent ID from Zustand store
  const { voiceAgentId, setVoiceAgentId, setAgentName, resetStore } =
    useAgentStore();

  // Track the selected agent with icon and color
  const [selectedAgent, setSelectedAgent] = useState<AgentWithVisuals | null>(
    null
  );

  // Track which agent is currently being deleted
  const [deletingAgentId, setDeletingAgentId] = useState<string | null>(null);

  // Alert dialog for delete confirmation
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [agentToDelete, setAgentToDelete] = useState<AgentWithVisuals | null>(
    null
  );

  // Redirect to create agent page if no agents
  useEffect(() => {
    if (
      voiceAgents &&
      Array.isArray(voiceAgents) &&
      voiceAgents.length === 0 &&
      !isLoading &&
      !error
    ) {
      router.push("/create-agent");
    }
  }, [voiceAgents, isLoading, error, router]);

  // Initialize selected agent when data loads
  useEffect(() => {
    if (
      !isLoading &&
      voiceAgents &&
      Array.isArray(voiceAgents) &&
      voiceAgents.length > 0
    ) {
      // First check if the current agent ID is valid
      const storedAgentValid =
        voiceAgentId &&
        voiceAgentId !== "" &&
        voiceAgents.some((agent) => agent.id === voiceAgentId);

      if (storedAgentValid) {
        // Use the stored agent if it's valid
        const agent = voiceAgents.find((a) => a.id === voiceAgentId)!;

        setSelectedAgent({
          id: agent.id,
          name: agent.name,
          icon: getInitials(agent.name),
          color: generateColor(agent.name),
          systemContext: agent.systemContext,
        });

        console.log("Using stored agent:", agent.name);
      } else {
        // If no valid agent in the store, use the first one
        const firstAgent = voiceAgents[0];

        setSelectedAgent({
          id: firstAgent.id,
          name: firstAgent.name,
          icon: getInitials(firstAgent.name),
          color: generateColor(firstAgent.name),
          systemContext: firstAgent.systemContext,
        });

        // Important: Update the store with the first agent
        setVoiceAgentId(firstAgent.id);
        setAgentName(firstAgent.name);

        console.log("Setting default agent:", firstAgent.name);
      }
    } else if (voiceAgents && voiceAgents.length === 0) {
      // Reset if no agents available
      setSelectedAgent(null);
      resetStore();
    }
  }, [
    voiceAgents,
    isLoading,
    voiceAgentId,
    setVoiceAgentId,
    setAgentName,
    resetStore,
  ]);

  // Handle agent selection
  const handleSelectAgent = (agent: AgentWithVisuals): void => {
    // Update local state
    setSelectedAgent(agent);

    // Update store
    setVoiceAgentId(agent.id);
    setAgentName(agent.name);

    console.log("Selected agent:", agent.name, agent.id);

    // Immediate store update via session storage for redundancy
    try {
      sessionStorage.setItem("last-selected-agent", agent.id);
    } catch (e) {
      console.error("Session storage error:", e);
    }
  };

  // Handle delete confirmation
  const confirmDelete = async (): Promise<void> => {
    if (!agentToDelete) return;

    try {
      setDeletingAgentId(agentToDelete.id);
      await deleteVoiceAgent(agentToDelete.id).unwrap();

      // If we deleted the currently selected agent, reset selection
      if (selectedAgent?.id === agentToDelete.id) {
        resetStore();
      }

      // Refresh the list
      refetch();

      // Close dialog
      setDeleteDialogOpen(false);
      setAgentToDelete(null);
    } catch (err) {
      console.error("Failed to delete agent:", err);
    } finally {
      setDeletingAgentId(null);
    }
  };

  // Open delete dialog
  const openDeleteDialog = (
    agent: AgentWithVisuals,
    event: React.MouseEvent
  ): void => {
    event.stopPropagation(); // Prevent dropdown item click
    setAgentToDelete(agent);
    setDeleteDialogOpen(true);
  };

  // Rest of the component (render logic) remains the same...
  // (Including loading states, error handling, and the UI elements)

  if (isLoading) {
    return (
      <Button
        variant="outline"
        className="w-full justify-between items-center border rounded-full px-4 py-2 h-12 animate-pulse"
        disabled
      >
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
            <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
          </div>
          <div className="h-4 w-32 bg-gray-300 rounded"></div>
        </div>
        <ChevronDown className="h-4 w-4 text-gray-300" />
      </Button>
    );
  }

  if (error) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between items-center border border-red-300 rounded-full px-4 py-2 h-12"
              onClick={() => refetch()}
            >
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <span className="text-red-500">Failed to load agents</span>
              </div>
              <div className="h-5 w-5 text-gray-400">↻</div>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Click to retry</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (!voiceAgents || !Array.isArray(voiceAgents) || voiceAgents.length === 0) {
    return (
      <Link href="/create-agent">
        <Button
          variant="outline"
          className="w-full justify-between items-center border border-dashed rounded-full px-4 py-2 h-12"
        >
          <div className="flex items-center gap-2">
            <Plus className="h-4 w-4 text-gray-500" />
            <span className="text-gray-500">Create your first voice agent</span>
          </div>
        </Button>
      </Link>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between items-center border rounded-full px-4 py-2 h-12"
          >
            {selectedAgent ? (
              <>
                <div className="flex items-center gap-2">
                  <div
                    className={`w-6 h-6 rounded-full ${selectedAgent.color} flex items-center justify-center`}
                  >
                    <span className="text-white text-xs">
                      {selectedAgent.icon}
                    </span>
                  </div>
                  <span>{selectedAgent.name}</span>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-500" />
              </>
            ) : (
              <>
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                    <span className="text-white text-xs">?</span>
                  </div>
                  <span>Select an agent</span>
                </div>
                <ChevronDown className="h-4 w-4 text-gray-500" />
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-80">
          {Array.isArray(voiceAgents) &&
            voiceAgents.map((agent) => {
              const visualAgent: AgentWithVisuals = {
                id: agent.id,
                name: agent.name,
                icon: getInitials(agent.name),
                color: generateColor(agent.name),
                systemContext: agent.systemContext,
              };

              return (
                <DropdownMenuItem
                  key={agent.id}
                  className="flex items-center justify-between py-2 px-3"
                >
                  <div
                    className="flex items-center gap-2 flex-1 cursor-pointer"
                    onClick={() => handleSelectAgent(visualAgent)}
                  >
                    <div
                      className={`w-5 h-5 rounded-full ${visualAgent.color} flex items-center justify-center`}
                    >
                      <span className="text-white text-xs">
                        {visualAgent.icon}
                      </span>
                    </div>
                    <span className="truncate">{visualAgent.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {selectedAgent?.id === agent.id && (
                      <Check className="h-4 w-4 mr-1" />
                    )}
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0 hover:bg-red-50 hover:text-red-500 rounded-full"
                            onClick={(e) => openDeleteDialog(visualAgent, e)}
                            disabled={isDeleting}
                          >
                            {deletingAgentId === agent.id ? (
                              <Loader2 className="h-3.5 w-3.5 animate-spin" />
                            ) : (
                              <Trash2 className="h-3.5 w-3.5" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Delete agent</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </DropdownMenuItem>
              );
            })}
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="flex items-center justify-center py-2 gap-2 cursor-pointer hover:bg-primary/10"
            asChild
          >
            <Link
              href="/create-agent"
              className="w-full flex items-center justify-center text-primary font-medium"
            >
              <Plus className="h-4 w-4 mr-1" />
              <span>Add New Agent</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Voice Agent</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{agentToDelete?.name}&quot;?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>Delete</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
