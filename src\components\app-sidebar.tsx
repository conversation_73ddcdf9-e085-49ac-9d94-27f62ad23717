"use client";
import * as React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { NavMain } from "@/components/nav-main";

import {
  Sidebar,
  SidebarContent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import { OperatorAIPromoCard } from "./sidebar-opt-in-form";
import Image from "next/image";
import { usePathname } from "next/navigation";
import logo from "../../public/images/verbal.webp";
import dashboardIcon from "../../public/images/Dash-icon.png";
import trainingIcon from "../../public/images/training-icon.png";
import integrationIcon from "../../public/images/integration.png";

// This is sample data.
const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: dashboardIcon,
    },
    {
      title: "Training & Sources",
      url: "/dashboard/training-sources",
      icon: trainingIcon,
    },
    {
      title: "Test Assistant",
      url: "/dashboard/test-assistant",
      icon: <PERSON><PERSON><PERSON>,
    },
    {
      title: "Settings",
      url: "/dashboard/settings",
      icon: Settings,
    },
    {
      title: "Integrations",
      url: "/dashboard/integrations",
      icon: integrationIcon,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { open } = useSidebar();
  const pathname = usePathname();

  // Create a version of navMain with active states based on current path
  const navMainWithActiveStates = React.useMemo(() => {
    return data.navMain.map((item) => ({
      ...item,
      isActive:
        pathname === item.url ||
        // Handle nested routes - consider parent active if path starts with the URL
        (item.url !== "/dashboard" && pathname?.startsWith(item.url)),
    }));
  }, [pathname]);

  return (
    <Sidebar
      collapsible="offcanvas"
      className="border-none overflow-hidden"
      {...props}
    >
      <div className="border-2 rounded-3xl border-border shadow-md py-2 h-full m-2 md:m-4 flex flex-col bg-white">
        <SidebarHeader className={`pt-4 md:pt-6 ${!open ? "px-2" : "px-4"}`}>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <a href="#" className="flex items-center gap-3">
                  <div
                    className={`flex ${
                      !open ? "size-12" : "size-36"
                    } items-center justify-center rounded-lg`}
                  >
                    <Image
                      src={logo}
                      alt="Verbal AI Logo"
                      className="w-full h-full object-contain"
                      sizes="(max-width: 768px) 100vw, 256px"
                      priority
                    />
                  </div>
                  <div className="flex flex-col gap-0.5 leading-none"></div>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent className="mt-4 pb-4">
          <NavMain items={navMainWithActiveStates} />
        </SidebarContent>
        <SidebarFooter className="mt-auto">
          {open && (
            <div className="mb-2">
              <OperatorAIPromoCard />
            </div>
          )}
          {/* <NavUser user={data.user} /> */}
        </SidebarFooter>
        <SidebarRail />
      </div>
    </Sidebar>
  );
}
