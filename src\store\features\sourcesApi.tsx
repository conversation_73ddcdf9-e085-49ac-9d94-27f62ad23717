import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { useAuthStore } from "@/store/features/useAuthStore";
import useAgentStore from "@/store/features/useAgentStore";

// Types for Source data
export interface Source {
  id: string;
  voice_agent_id: string;
  name: string;
  source_type: string;
  size?: string;
  url?: string;
  auto: boolean;
  auto_sync_interval?: string | null;
  auto_sync_last_trained?: string | null;
  last_modified?: string;
  status: string;
  created_at: string;
}

// Status update interface
interface SourceStatusUpdate {
  status: string;
}

// Source update interface
interface SourceUpdate {
  name?: string;
  auto?: boolean;
  auto_sync_interval?: string;
}

// Function to get token from Zustand store
const getZustandToken = (): string | null => {
  return useAuthStore.getState().token;
};

// Function to get voice agent ID from Zustand store
const getVoiceAgentId = (): string | null => {
  return useAgentStore.getState().voiceAgentId;
};

 const BASE_URL = process.env.NEXT_PUBLIC_API_URL || "";

export const sourcesApi = createApi({
  reducerPath: "sourcesApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: async (headers) => {
      // Get token from Zustand store
      const token = getZustandToken();

      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      headers.set("Content-Type", "application/json");
      return headers;
    },
  }),
  tagTypes: ["Source"],
  endpoints: (builder) => ({
    // Get all sources for a voice agent
    getSources: builder.query<Source[], { voiceAgentId: string } | undefined>({
      query: () => {
        const agentId = getVoiceAgentId();
        if (!agentId) {
          throw new Error("Voice agent ID is required");
        }
        return `/voice_agents/${agentId}/sources`;
      },
      providesTags: ["Source"],
    }),

    // Get a specific source by ID
    getSource: builder.query<Source, string>({
      query: (sourceId) => {
        const agentId = getVoiceAgentId();
        if (!agentId) {
          throw new Error("Voice agent ID is required");
        }
        return `/voice_agents/${agentId}/sources/${sourceId}`;
      },
      providesTags: ["Source"],
    }),

    // Update a source
    updateSource: builder.mutation<
      Source,
      { sourceId: string; data: SourceUpdate }
    >({
      query: ({ sourceId, data }) => {
        const agentId = getVoiceAgentId();
        if (!agentId) {
          throw new Error("Voice agent ID is required");
        }
        return {
          url: `/voice_agents/${agentId}/sources/${sourceId}`,
          method: "PUT",
          body: data,
        };
      },
      invalidatesTags: ["Source"],
    }),

    // Delete a source
    deleteSource: builder.mutation<void, string>({
      query: (sourceId) => {
        const agentId = getVoiceAgentId();
        if (!agentId) {
          throw new Error("Voice agent ID is required");
        }
        return {
          url: `/voice_agents/${agentId}/sources/${sourceId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: ["Source"],
    }),

    // Get source status
    getSourceStatus: builder.query<SourceStatusUpdate, string>({
      query: (sourceId) => {
        const agentId = getVoiceAgentId();
        if (!agentId) {
          throw new Error("Voice agent ID is required");
        }
        return `/voice_agents/${agentId}/sources/${sourceId}/status`;
      },
      providesTags: ["Source"],
    }),

    // Update source status
    updateSourceStatus: builder.mutation<
      void,
      { sourceId: string; status: SourceStatusUpdate }
    >({
      query: ({ sourceId, status }) => {
        const agentId = getVoiceAgentId();
        if (!agentId) {
          throw new Error("Voice agent ID is required");
        }
        return {
          url: `/voice_agents/${agentId}/sources/${sourceId}/status`,
          method: "PUT",
          body: status,
        };
      },
      invalidatesTags: ["Source"],
    }),
  }),
});

export const {
  useGetSourcesQuery,
  useGetSourceQuery,
  useUpdateSourceMutation,
  useDeleteSourceMutation,
  useGetSourceStatusQuery,
  useUpdateSourceStatusMutation,
} = sourcesApi;
