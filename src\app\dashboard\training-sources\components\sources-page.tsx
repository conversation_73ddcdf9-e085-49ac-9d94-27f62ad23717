"use client";
import React, { useState, useEffect } from "react";
import {
  Search,
  Plus,
  SlidersHorizontal,
  Loader2,
  Globe,
  ExternalLink,
  Trash2,
  FileText,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Pagination } from "./pagination";
import { FileUploadTab } from "./file-upload";
import { EmptySourcesState } from "./sources-empty-state";
import Image from "next/image";
import poweredby from "../../../../../public/images/powerby.png";
import {
  useGetSourcesQuery,
  useDeleteSourceMutation,
  useUpdateSourceMutation,
  Source,
} from "@/store/features/sourcesApi";
import { toast } from "sonner";
import { formatDate } from "./formatdate";
import { Textarea } from "@/components/ui/textarea";
import useTrainingStore from "@/store/features/useTrainingStore";
import useAgentStore from "@/store/features/useAgentStore";
import {
  useInitTextTrainingMutation,
  useInitTrainingMutation,
  useScrapeUrlsMutation,
  useUploadTrainingFilesMutation,
} from "@/store/features/trainingApi";
import { cancelScan, scanUrl } from "@/store/features/url-scanner-service";
import { AudioUploadTab } from "./audio-upload-tab";

// Source data interface
interface SourceData {
  id: string;
  name: string;
  source: string;
  type: string;
  size: string;
  status: string;
  auto: string;
  lastTrained: string;
}

// Props interface
interface SourcesPageProps {
  showAddDialog?: boolean;
  setShowAddDialog: (show: boolean) => void;
  onAddNewClick?: () => void;
}

// Items per page constant
const ITEMS_PER_PAGE = 10;

export function SourcesPage({
  showAddDialog = false,
  setShowAddDialog,
}: SourcesPageProps) {
  // Get state from training store
  const {
    scannedUrls,
    isLoading: isScanningUrl,
    scrapeProgress,
    streamStatus,
    scrappingId,
    removeUrl,
    addUrl,
    uploadedFiles,
    removeUploadedFile,
    setScrappingId,
    addUploadedFile,
  } = useTrainingStore();

  // Get voice agent ID from store
  const voiceAgentId = useAgentStore((state) => state.voiceAgentId);

  // State variables
  const [filterText, setFilterText] = useState<string>("");
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [selectedTab, setSelectedTab] = useState<string>("websites");
  const [websiteUrl, setWebsiteUrl] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [websitesInnerTab, setWebsitesInnerTab] = useState<string>("crawl");
  const [directUrls, setDirectUrls] = useState<string>("");
  const [selectedScannedUrls, setSelectedScannedUrls] = useState<string[]>([]);
  const [isTraining, setIsTraining] = useState(false);
  const [trainingStatus, setTrainingStatus] = useState("");
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [textTrainingStatus, setTextTrainingStatus] = useState<{
    message: string;
    type: "success" | "error";
  } | null>(null);

  // RTK Query hooks

  const {
    data: apiSources,
    isLoading,
    error,
    refetch,
  } = useGetSourcesQuery(
    voiceAgentId ? { voiceAgentId } : undefined, // Only fetch if voiceAgentId exists
    {
      skip: !voiceAgentId,
    }
  );
  const [deleteSource, { isLoading: isDeleting }] = useDeleteSourceMutation();
  const [updateSource, { isLoading: isUpdating }] = useUpdateSourceMutation();
  const [initTextTraining] = useInitTextTrainingMutation();

  // Training API hooks
  const [initTraining] = useInitTrainingMutation();
  const [uploadTrainingFiles, { isLoading: isUploadingFiles }] =
    useUploadTrainingFilesMutation();
  const [scrapeUrls, { isLoading: isScraping }] = useScrapeUrlsMutation();

  // Initialize selected scanned URLs with all scanned URLs
  useEffect(() => {
    setSelectedScannedUrls([...scannedUrls]);
  }, [scannedUrls]);

  // Transform API data to component format
  const formatSources = (): SourceData[] => {
    if (!apiSources) return [];

    return apiSources.map((source: Source) => ({
      id: source.id,
      name: source.name,
      source: source.url || "Unknown",
      type: source.source_type.toUpperCase(),
      size: source.size || "Unknown",
      status: source.status === "done" ? "Trained" : source.status,
      auto: source.auto ? "Yes" : "No",
      lastTrained:
        source.auto_sync_last_trained || source.last_modified || "Unknown",
    }));
  };

  // Filter sources based on filter text
  const filteredSources = formatSources().filter(
    (source) =>
      source.name.toLowerCase().includes(filterText.toLowerCase()) ||
      source.source.toLowerCase().includes(filterText.toLowerCase())
  );

  // Pagination calculations
  const totalSources = filteredSources.length;
  const totalPages = Math.ceil(totalSources / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const paginatedSources = filteredSources.slice(
    startIndex,
    startIndex + ITEMS_PER_PAGE
  );

  // Source data to display
  const sources = paginatedSources;

  // Check if there are sources
  const hasSources = totalSources > 0;

  // Reset selected rows when sources or page changes
  useEffect(() => {
    setSelectedRows([]);
  }, [apiSources, currentPage]);

  // Monitor scrape progress and show toast
  useEffect(() => {
    if (isScanningUrl && scrapeProgress > 0) {
      toast(`${streamStatus} - ${scrapeProgress}%`);
    }

    if (scrapeProgress === 100) {
      toast("Website scanning completed!");
    }
  }, [isScanningUrl, scrapeProgress, streamStatus]);

  const handleSelectAllChange = (checked: boolean) => {
    if (checked) {
      setSelectedRows(sources.map((source) => source.id));
    } else {
      setSelectedRows([]);
    }
  };

  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, id]);
    } else {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id));
    }
  };

  const handleAddNewClick = () => {
    setShowAddDialog(true);
  };

  const [urlFilter, setUrlFilter] = useState<string>("");

  const filteredScannedUrls = scannedUrls.filter((url) =>
    url.toLowerCase().includes(urlFilter.toLowerCase())
  );
  // Handle scanned URL selection
  const handleScannedUrlSelect = (url: string, checked: boolean) => {
    if (checked) {
      setSelectedScannedUrls([...selectedScannedUrls, url]);
    } else {
      setSelectedScannedUrls(selectedScannedUrls.filter((u) => u !== url));
    }
  };

  // Handle URL scanning
  const handleScanUrl = async () => {
    if (!websiteUrl) {
      toast("Please enter a website URL");
      return;
    }

    try {
      // Start scanning
      toast(`Starting to scan ${websiteUrl}...`);
      await scanUrl(websiteUrl);

      // Clear input fields after successful scan start
      setWebsiteUrl("");
    } catch (error) {
      toast(
        "Error scanning URL: " +
          (error instanceof Error ? error.message : "Unknown error occurred")
      );
    }
  };

  // Handle direct URL list submission
  const handleDirectUrls = async () => {
    if (!directUrls.trim()) {
      toast("Please enter at least one URL");
      return;
    }

    try {
      // Parse URLs from textarea
      const urls = directUrls
        .split("\n")
        .map((url) => url.trim())
        .filter((url) => url);

      if (urls.length === 0) {
        toast("No valid URLs found");
        return;
      }

      // Show loading toast
      toast(`Processing ${urls.length} URLs...`);

      const response = await scrapeUrls({
        urls: urls,
      }).unwrap();

      // Update the store with the scraped URLs
      urls.forEach((url) => addUrl(url));

      // Set the scrapping ID from the response
      if (response.scrapping_id) {
        setScrappingId(response.scrapping_id);
      }

      toast(`Added ${urls.length} URLs successfully`);

      // Clear input
      setDirectUrls("");
    } catch (error) {
      toast(
        "Error adding URLs: " +
          (error instanceof Error ? error.message : "Unknown error occurred")
      );
    }
  };

  // Handle cancel scan
  const handleCancelScan = () => {
    cancelScan();
    toast("Scan cancelled");
  };

  // Handle file upload for PDFs
  const handleFileUpload = (files: FileList) => {
    const pdfFiles = Array.from(files).filter(
      (file) =>
        file.type === "application/pdf" ||
        file.name.toLowerCase().endsWith(".pdf")
    );

    if (pdfFiles.length === 0) {
      toast("Please select PDF files only");
      return;
    }

    // Add files to store
    pdfFiles.forEach((file) => addUploadedFile(file));

    toast.success(`Added ${pdfFiles.length} PDF file(s)`);
  };

  // Remove a PDF file
  const handleRemovePdf = (index: number) => {
    removeUploadedFile(index);
    toast.success("File removed");
  };

  // Remove a URL
  const handleRemoveUrl = (url: string) => {
    removeUrl(url);
    setSelectedScannedUrls((prev) => prev.filter((u) => u !== url));
    toast.success("URL removed");
  };

  // Clear all URLs
  const handleClearAllUrls = () => {
    scannedUrls.forEach((url) => removeUrl(url));
    setSelectedScannedUrls([]);
    toast.success("All URLs cleared");
  };

  // Clear all PDFs
  const handleClearAllPdfs = () => {
    uploadedFiles.forEach(() => removeUploadedFile(0));
    toast.success("All PDF files cleared");
  };

  // Start training with selected sources
  const handleStartTraining = async () => {
    if (!voiceAgentId) {
      toast("No voice agent ID found. Please create an agent first.");
      return;
    }

    const hasUrls = selectedScannedUrls.length > 0;
    const hasFiles = uploadedFiles.length > 0;

    if (!hasUrls && !hasFiles) {
      toast(
        "Please select at least one URL or upload a PDF file for training."
      );
      return;
    }

    setIsTraining(true);
    setTrainingStatus("Initializing training...");
    toast("Initializing training...");

    let success = true;

    try {
      // Process URL training if we have URLs
      if (hasUrls && scrappingId) {
        setTrainingStatus("Processing website data...");
        toast("Processing website data...");

        await initTraining({
          voiceAgentId,
          data: {
            data: selectedScannedUrls,
            scrappingId,
          },
        }).unwrap();
      }

      // Process file training if we have files
      if (hasFiles) {
        setTrainingStatus("Processing PDF documents...");
        toast("Processing PDF documents...");

        await uploadTrainingFiles({
          voiceAgentId,
          files: uploadedFiles,
        }).unwrap();
      }

      toast("Training initiated successfully!");
      setTrainingStatus("Training in progress. This may take a few minutes.");

      // After a successful training initiation, clear the sources and close dialog
      setTimeout(() => {
        setSelectedScannedUrls([]);
        scannedUrls.forEach((url) => removeUrl(url));
        uploadedFiles.forEach(() => removeUploadedFile(0));
        setShowAddDialog(false);
        setTrainingStatus("");
        setIsTraining(false);
        refetch(); // Refresh the sources list
      }, 2000);
    } catch (err) {
      console.error("Error initiating training:", err);
      toast("Failed to initiate training. Please try again.");
      setTrainingStatus("");
      success = false;
    } finally {
      setIsTraining(false);
      if (!success) {
        setTrainingStatus("");
      }
    }
  };

  const handleDeleteSelected = async () => {
    try {
      toast(`Deleting ${selectedRows.length} source(s)...`);

      // Delete each selected source one by one
      for (const sourceId of selectedRows) {
        await deleteSource(sourceId).unwrap();
      }

      toast(`Successfully deleted ${selectedRows.length} source(s)`);
      setSelectedRows([]);
      refetch();
    } catch (error) {
      toast("Failed to delete sources");
      console.error("Delete error:", error);
    }
  };

  const handleRetrainSelected = async () => {
    try {
      toast(`Retraining ${selectedRows.length} source(s)...`);

      // For each selected source, update to trigger retraining
      for (const sourceId of selectedRows) {
        await updateSource({
          sourceId,
          data: { name: "Retrained" }, // This would need to be adjusted based on actual API
        }).unwrap();
      }

      toast(`Retraining ${selectedRows.length} source(s)`);
      setSelectedRows([]);
      refetch();
    } catch (error) {
      toast("Failed to retrain sources");
      console.error("Retrain error:", error);
    }
  };

  const handleAutoSyncToggle = async () => {
    try {
      toast(`Updating auto sync for ${selectedRows.length} source(s)...`);

      // Toggle auto sync for selected sources
      for (const sourceId of selectedRows) {
        const source = apiSources?.find((s) => s.id === sourceId);
        if (source) {
          await updateSource({
            sourceId,
            data: { auto: !source.auto },
          }).unwrap();
        }
      }

      toast(`Updated auto sync for ${selectedRows.length} source(s)`);
      setSelectedRows([]);
      refetch();
    } catch (error) {
      toast("Failed to update auto sync settings");
      console.error("Auto sync error:", error);
    }
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // When page changes, reset selected rows
    setSelectedRows([]);
  };

  const handleClear = () => {
    setTitle("");
    setContent("");
    setTextTrainingStatus(null);
  };

  const handleSubmitTextTraining = async () => {
    if (!title || !content) return;

    setIsSubmitting(true);
    setTextTrainingStatus(null);

    try {
      const response = await initTextTraining({
        voiceAgentId: voiceAgentId as string,
        data: {
          name: title,
          data: content,
        },
      }).unwrap();

      setTextTrainingStatus({
        message: `Training initiated successfully. ${response.message || ""}`,
        type: "success",
      });

      toast.success("Text training initiated successfully!");
      handleClear();
    } catch (error) {
      console.error("Error submitting text training:", error);
      setTextTrainingStatus({
        message: "Failed to initiate text training. Please try again.",
        type: "error",
      });
      toast.error("Failed to initiate text training");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        Loading sources...
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center h-64 flex-col">
        <p className="text-red-500 mb-4">Error loading sources</p>
        <Button onClick={() => refetch()}>Retry</Button>
      </div>
    );
  }

  // // Empty state
  // if (!hasSources) {
  //   return (
  //     <EmptySourcesState
  //       onAddNewSource={() => {
  //         console.log("Direct click handler");
  //         setShowAddDialog(true);
  //       }}
  //     />
  //   );
  // }

  return (
    <div className="w-full flex flex-col">
      {/* Main content */}
      {!hasSources ? (
        <EmptySourcesState
          onAddNewSource={() => {
            console.log("Direct click handler");
            setShowAddDialog(true);
          }}
        />
      ) : (
        <div className="p-6 border rounded-lg m-4">
          <div className="flex justify-between items-center mb-2">
            <div>
              <h1 className="text-2xl font-bold">Source</h1>
              <p className="text-gray-500 text-sm mt-1">
                View all the sources you&apos;ve added to your chatbot. You can
                retrain or delete them individually or in bulk.
              </p>
            </div>
            <Button
              onClick={handleAddNewClick}
              className="bg-primary hover:bg-orange-600 text-white rounded-lg"
            >
              Add New Source <Plus className="ml-2 h-4 w-4" />
            </Button>
          </div>

          <div className="mt-6 overflow-hidden">
            <div className="p-4 flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Filter URLs..."
                  className="pl-9 rounded-md"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                />
              </div>
              <Button variant="outline" className="gap-2">
                <SlidersHorizontal className="h-4 w-4" />
                Type
              </Button>
            </div>

            {selectedRows.length > 0 ? (
              <div className="bg-orange-50 p-2 border-y flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="rounded-md"
                  onClick={handleDeleteSelected}
                  disabled={isDeleting}
                >
                  Delete
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="rounded-md"
                  onClick={handleRetrainSelected}
                  disabled={isUpdating}
                >
                  Retrain
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="rounded-md"
                  onClick={handleAutoSyncToggle}
                  disabled={isUpdating}
                >
                  Auto Sync
                </Button>
                <div className="ml-2 text-sm text-gray-500">
                  {selectedRows.length} source(s) selected
                </div>
              </div>
            ) : null}

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={
                        selectedRows.length === sources.length &&
                        sources.length > 0
                      }
                      onCheckedChange={handleSelectAllChange}
                    />
                  </TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead className="text-right">Size</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Auto</TableHead>
                  <TableHead className="text-right">Last Trained</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sources.map((source) => (
                  <TableRow key={source.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedRows.includes(source.id)}
                        onCheckedChange={(checked) =>
                          handleRowSelect(source.id, checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell className="font-medium text-ellipsis">
                      {source.name}
                    </TableCell>
                    <TableCell className="text-wrap">
                      <a
                        href={source.source}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline flex items-center"
                      >
                        {source.source}
                        <ExternalLink size={14} className="ml-1" />
                      </a>
                    </TableCell>
                    <TableCell>{source.type}</TableCell>
                    <TableCell className="text-right">{source.size}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div
                          className={`w-2 h-2 rounded-full mr-2 ${
                            source.status === "Trained"
                              ? "bg-green-500"
                              : source.status === "Failed"
                              ? "bg-red-500"
                              : "bg-yellow-500"
                          }`}
                        ></div>
                        {source.status}
                      </div>
                    </TableCell>
                    <TableCell>{source.auto}</TableCell>
                    <TableCell className="text-right">
                      {formatDate(source.lastTrained)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="p-4 flex items-center justify-between border-t">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        </div>
      )}

      {/* Powered by image at the bottom of the page */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center mt-20 bg-white">
        <Image
          src={poweredby}
          alt="Powered by Texagon"
          style={{ width: "auto", height: "auto" }}
        />
      </div>

      {/* Add New Source Dialog - Always rendered */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl overflow-auto max-h-[80%]">
          <DialogHeader>
            <div className="flex justify-between items-center">
              <DialogTitle className="text-xl">
                Add New Training Sources
              </DialogTitle>
            </div>
            <DialogDescription>
              Enhance your assistant&apos;s knowledge by uploading content from
              websites, sitemaps, links, PDFs, CSVs, text files, audio files,
              and more!
            </DialogDescription>
          </DialogHeader>

          <Tabs
            defaultValue="websites"
            value={selectedTab}
            onValueChange={setSelectedTab}
          >
            <TabsList className="grid grid-cols-6 mb-4 border-b space-x-0">
              <TabsTrigger
                value="websites"
                className="data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:shadow-none rounded-none"
              >
                Websites
              </TabsTrigger>
              <TabsTrigger
                value="pdfs"
                className="data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:shadow-none rounded-none"
              >
                PDFs
              </TabsTrigger>
              <TabsTrigger
                value="text"
                className="data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:shadow-none rounded-none"
              >
                Text
              </TabsTrigger>
              <TabsTrigger
                value="csv"
                className="data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:shadow-none rounded-none"
              >
                CSV
              </TabsTrigger>
              <TabsTrigger
                value="audio"
                className="data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:shadow-none rounded-none"
              >
                Audio
              </TabsTrigger>
              <TabsTrigger
                value="apps"
                className="data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:shadow-none rounded-none"
              >
                Apps
              </TabsTrigger>
            </TabsList>

            <TabsContent value="websites">
              <div>
                <h3 className="text-lg font-medium">Websites Sources</h3>
                <p className="text-sm text-gray-500 mt-1 mb-4">
                  Add a website, sitemap, or links to train your assistant.
                  We&apos;ll extract text from the pages, which may take a
                  minute or two depending on the content size.
                </p>

                <Tabs
                  defaultValue="crawl"
                  value={websitesInnerTab}
                  onValueChange={setWebsitesInnerTab}
                  className="w-full mb-6"
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="crawl">Crawl Website</TabsTrigger>
                    <TabsTrigger value="add">Add URLs</TabsTrigger>
                  </TabsList>

                  <TabsContent value="crawl" className="pt-4">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          Website URL
                        </label>
                        <div className="flex gap-2 mb-1">
                          <Input
                            placeholder="exampleurl.com"
                            className="flex-1 rounded-lg"
                            value={websiteUrl}
                            onChange={(e) => setWebsiteUrl(e.target.value)}
                            disabled={isScanningUrl}
                          />
                          <Button
                            className="bg-orange-500 hover:bg-orange-600 text-white"
                            onClick={handleScanUrl}
                            disabled={isScanningUrl || !websiteUrl}
                          >
                            {isScanningUrl ? (
                              <div className="flex items-center">
                                <Loader2 className="animate-spin mr-2 h-4 w-4" />
                                Crawling...
                              </div>
                            ) : (
                              "Crawl Website"
                            )}
                          </Button>
                        </div>
                        <p className="text-xs text-gray-500 mb-4">
                          Provide website URL to crawl and extract content
                        </p>
                      </div>

                      {isScanningUrl && (
                        <Button
                          variant="destructive"
                          onClick={handleCancelScan}
                          className="w-full"
                        >
                          Cancel Scan
                        </Button>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="add" className="pt-4">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          Multiple URLs
                        </label>
                        <Textarea
                          placeholder="Enter URLs (one per line)"
                          className="w-full rounded-lg min-h-[150px]"
                          value={directUrls}
                          onChange={(e) => setDirectUrls(e.target.value)}
                          disabled={isScraping}
                        />
                        <p className="text-xs text-gray-500 mt-1 mb-4">
                          Enter each URL on a new line
                        </p>
                      </div>

                      <Button
                        className="w-full bg-orange-500 hover:bg-orange-600 text-white"
                        onClick={handleDirectUrls}
                        disabled={!directUrls.trim() || isScraping}
                      >
                        {isScraping ? (
                          <div className="flex items-center">
                            <Loader2 className="animate-spin mr-2 h-4 w-4" />
                            Processing...
                          </div>
                        ) : (
                          "Add URLs"
                        )}
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>

                {/* Scanned URLs section */}
                {scannedUrls.length > 0 ? (
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-md font-medium">
                        Scanned URLs ({selectedScannedUrls.length}/
                        {scannedUrls.length})
                      </h4>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedScannedUrls([])}
                          disabled={selectedScannedUrls.length === 0}
                          className="text-xs"
                        >
                          Clear Selection
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setSelectedScannedUrls([...scannedUrls])
                          }
                          disabled={
                            selectedScannedUrls.length === scannedUrls.length
                          }
                          className="text-xs"
                        >
                          Select All
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleClearAllUrls}
                          className="text-xs text-red-500"
                        >
                          Remove All
                        </Button>
                      </div>
                    </div>

                    {/* URL filter input */}
                    <div className="mb-3">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                          placeholder="Filter URLs..."
                          className="pl-9 rounded-md"
                          value={urlFilter}
                          onChange={(e) => setUrlFilter(e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2 max-h-60 overflow-y-auto pr-2 rounded-lg border border-gray-100 p-2 bg-gray-50">
                      {filteredScannedUrls.length > 0 ? (
                        filteredScannedUrls.map((url, index) => (
                          <div
                            key={index}
                            className={`flex items-center justify-between p-3 rounded-lg border ${
                              selectedScannedUrls.includes(url)
                                ? "border-orange-500 bg-white shadow-sm"
                                : "border-gray-200 bg-white"
                            } transition-colors duration-200`}
                          >
                            <div className="flex items-center gap-3 overflow-hidden">
                              <Checkbox
                                checked={selectedScannedUrls.includes(url)}
                                onCheckedChange={(checked) =>
                                  handleScannedUrlSelect(
                                    url,
                                    checked as boolean
                                  )
                                }
                              />
                              <div className="truncate text-sm">
                                <a
                                  href={url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="hover:underline flex items-center gap-1"
                                >
                                  {url}
                                  <ExternalLink size={12} />
                                </a>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 rounded-full text-gray-400 hover:text-red-500"
                              onClick={() => handleRemoveUrl(url)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          </div>
                        ))
                      ) : (
                        <div className="flex flex-col items-center justify-center py-8 text-center text-gray-500">
                          <Search className="h-10 w-10 mb-2 text-gray-300" />
                          <p className="font-medium">No matching URLs found</p>
                          <p className="text-sm">Try adjusting your filter</p>
                        </div>
                      )}
                    </div>

                    {/* Training button only shown when there are selected URLs */}
                    <Button
                      className="w-full mt-4 bg-primary hover:bg-primary/80 text-white"
                      onClick={handleStartTraining}
                      disabled={selectedScannedUrls.length === 0 || isTraining}
                    >
                      {isTraining ? (
                        <div className="flex items-center">
                          <Loader2 className="animate-spin mr-2 h-4 w-4" />
                          {trainingStatus || "Training..."}
                        </div>
                      ) : (
                        "Train Selected URLs"
                      )}
                    </Button>
                  </div>
                ) : (
                  <div className="mb-4">
                    <div className="flex flex-col items-center justify-center py-12 text-center border-2 border-dashed border-gray-200 rounded-lg">
                      <Globe className="h-12 w-12 text-gray-300 mb-3" />
                      <h4 className="text-lg font-medium mb-1">
                        No URLs scanned yet
                      </h4>
                      <p className="text-gray-500 mb-4 max-w-md">
                        Use the options above to scan a website or add URLs
                        directly to get started.
                      </p>
                      {websitesInnerTab === "crawl" ? (
                        <Button
                          className="bg-orange-500 hover:bg-orange-600 text-white"
                          onClick={() => setWebsitesInnerTab("add")}
                        >
                          Add URLs Directly
                        </Button>
                      ) : (
                        <Button
                          className="bg-orange-500 hover:bg-orange-600 text-white"
                          onClick={() => setWebsitesInnerTab("crawl")}
                        >
                          Crawl a Website
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="pdfs">
              <div>
                <h3 className="text-lg font-medium">PDFs Upload</h3>
                <p className="text-sm text-gray-500 mt-1 mb-4">
                  Upload PDF files to train your assistant. We&apos;ll extract
                  text from the PDFs to enhance your assistant&apos;s knowledge.
                </p>

                {/* File upload section */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-6">
                  <input
                    type="file"
                    id="pdf-upload"
                    multiple
                    accept=".pdf"
                    className="hidden"
                    onChange={(e) =>
                      e.target.files && handleFileUpload(e.target.files)
                    }
                    disabled={isUploadingFiles}
                  />
                  <label
                    htmlFor="pdf-upload"
                    className="cursor-pointer flex flex-col items-center justify-center"
                  >
                    <FileText className="h-10 w-10 text-gray-400 mb-2" />
                    <span className="font-medium">
                      Click to upload PDF files
                    </span>
                    <span className="text-sm text-gray-500">
                      or drag and drop files here
                    </span>
                    {isUploadingFiles && (
                      <Loader2 className="animate-spin h-5 w-5 mt-2 text-orange-500" />
                    )}
                  </label>
                </div>

                {/* Uploaded files section */}
                {uploadedFiles.length > 0 && (
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-md font-medium">
                        Uploaded PDFs ({uploadedFiles.length})
                      </h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleClearAllPdfs}
                        className="text-xs text-red-500"
                      >
                        Remove All
                      </Button>
                    </div>

                    <div className="space-y-2 max-h-60 overflow-y-auto pr-2 rounded-lg border border-gray-100 p-2 bg-gray-50">
                      {uploadedFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 rounded-lg border border-blue-200 bg-white shadow-sm"
                        >
                          <div className="flex items-center gap-3 overflow-hidden">
                            <FileText className="shrink-0 text-blue-600" />
                            <div className="truncate text-sm">
                              <p className="font-medium truncate">
                                {file.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {formatFileSize(file.size)}
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 rounded-full text-gray-400 hover:text-red-500"
                            onClick={() => handleRemovePdf(index)}
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                      ))}
                    </div>

                    {/* Training button for PDFs */}
                    <Button
                      className="w-full mt-4 bg-green-600 hover:bg-green-700 text-white"
                      onClick={handleStartTraining}
                      disabled={isTraining}
                    >
                      {isTraining ? (
                        <div className="flex items-center">
                          <Loader2 className="animate-spin mr-2 h-4 w-4" />
                          {trainingStatus || "Training..."}
                        </div>
                      ) : (
                        "Train PDF Documents"
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="text">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Text Sources</h3>
                  <p className="text-sm text-gray-500 mt-1 mb-4">
                    Add a website, sitemap, or links to train your assistant.
                    We&apos;ll extract text from the pages, which may take a
                    minute or two depending on the content size.
                  </p>
                </div>
                <div>
                  <label
                    htmlFor="title"
                    className="block text-sm font-medium mb-1"
                  >
                    Title
                  </label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter a title for your text content"
                    className="w-full rounded-lg"
                  />
                </div>

                <div>
                  <label
                    htmlFor="content"
                    className="block text-sm font-medium mb-1"
                  >
                    Content
                  </label>
                  <Textarea
                    id="content"
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder="Enter your training content here"
                    className="w-full min-h-[150px] rounded-lg"
                  />
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={handleClear}
                    className="rounded-lg"
                    disabled={isSubmitting}
                  >
                    Clear
                  </Button>
                  <Button
                    onClick={handleSubmitTextTraining}
                    className="bg-orange-500 hover:bg-orange-600 text-white rounded-lg"
                    disabled={isSubmitting || !title || !content}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      "Add Text"
                    )}
                  </Button>
                </div>

                {textTrainingStatus && (
                  <div
                    className={`mt-4 p-4 rounded-lg ${
                      textTrainingStatus.type === "error"
                        ? "bg-red-50 text-red-600"
                        : "bg-green-50 text-green-600"
                    }`}
                  >
                    {textTrainingStatus.message}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="csv">
              <FileUploadTab
                title="CSV Upload"
                description="Add CSV files to train your assistant. We'll extract structured data from the files to enhance your assistant's knowledge."
                acceptTypes=".csv,.xls,.xlsx"
                uploadButtonText="Train CSV Data"
              />
            </TabsContent>

            <TabsContent value="audio">
              <AudioUploadTab
                title="Audio Upload"
                description="Add audio files to train your assistant. We'll transcribe and extract content from the audio to enhance your assistant's knowledge."
                acceptTypes=".mp3,.mp4,.mpeg,.mpga,.m4a,.wav,.webm"
                uploadButtonText="Process Audio Files"
              />
            </TabsContent>
            <TabsContent value="apps">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Apps</h3>
                  <p className="text-sm text-gray-500 mt-1 mb-4">
                    Connect your assistant to apps and platforms to enhance its
                    training. More integrations like Google Docs are coming
                    soon!
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <div className="flex flex-col items-center justify-center p-6 bg-orange-50 rounded-lg border border-orange-100 hover:bg-orange-100 transition-colors cursor-pointer">
                    <div className="w-10 h-10 bg-black rounded-md flex items-center justify-center mb-3">
                      <FileText className="h-5 w-5 text-white" />
                    </div>
                    <span className="text-sm font-medium">
                      Connect Google Docs
                    </span>
                  </div>

                  <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-100">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                      <Plus className="h-5 w-5 text-gray-500" />
                    </div>
                    <span className="text-sm font-medium text-gray-600">
                      Apps Coming Soon
                    </span>
                  </div>

                  <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-100">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mb-3">
                      <Plus className="h-5 w-5 text-gray-500" />
                    </div>
                    <span className="text-sm font-medium text-gray-600">
                      Apps Coming Soon
                    </span>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </div>
  );
}
