"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useState } from "react";
import useAgentStore from "@/store/features/useAgentStore";
import { useDeleteVoiceAgentMutation } from "@/store/features/voiceAgentApi";
import { AlertDialog } from "@radix-ui/react-alert-dialog";
import { AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

export default function SettingsPage() {

	 const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
   const { toast } = useToast();
   const router = useRouter();

   // Get agent details from store
   const { voiceAgentId, agentName, resetStore } = useAgentStore();

   // Setup delete mutation from API
   const [deleteVoiceAgent, { isLoading: isDeleting }] =
     useDeleteVoiceAgentMutation();

   const handleDeleteClick = () => {
     setIsDeleteDialogOpen(true);
   };

   const handleCancelDelete = () => {
     setIsDeleteDialogOpen(false);
   };

   const handleConfirmDelete = async () => {
     if (!voiceAgentId) {
       toast({
         title: "Error",
         description: "No chatbot selected for deletion",
         variant: "destructive",
       });
       setIsDeleteDialogOpen(false);
       return;
     }

     try {
       await deleteVoiceAgent(voiceAgentId).unwrap();

       // Reset the store after successful deletion
       resetStore();

       toast({
         title: "Success",
         description: `${
           agentName || "Chatbot"
         } has been deleted successfully.`,
       });

       // Redirect to main page or agents list
       router.push("/dashboard");
		 } catch (error) {
			 console.log(error)
       toast({
         title: "Failed to delete",
         description:
           "There was an error deleting the chatbot. Please try again.",
         variant: "destructive",
       });
     } finally {
       setIsDeleteDialogOpen(false);
     }
   };
  return (
    <div className="max-w-3xl  p-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold tracking-tight">
            Chatbot Settings
          </h1>
          <p className="text-muted-foreground">
            Update the settings, prompts and more for your chatbot. Be careful,
            these settings can have a big impact on the user experience.
          </p>
        </div>

        <Tabs defaultValue="basic" className="w-full">
          <div className="overflow-x-auto">
            <TabsList className="w-full inline-flex whitespace-nowrap">
              <TabsTrigger
                value="basic"
                className="data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none flex-1"
              >
                Basic
              </TabsTrigger>
              <TabsTrigger
                value="escalation"
                className="data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none flex-1"
              >
                Escalation
              </TabsTrigger>
              <TabsTrigger
                value="scheduling"
                className="data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none flex-1 text-sm md:text-base"
              >
                Scheduling & Appointments
              </TabsTrigger>
              <TabsTrigger
                value="delete"
                className="data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none flex-1"
              >
                Delete
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="basic" className="pt-6 space-y-6">
            <div className="space-y-1">
              <h2 className="text-lg font-semibold">Basic Settings</h2>
              <p className="text-sm text-muted-foreground">
                Add a website, sitemap, or links to train your assistant.
                We&apos;ll extract text from the pages, which may take a minute
                or two depending on the content size
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="operator-name" className="font-medium">
                  Operator Name
                </label>
                <p className="text-sm text-muted-foreground">
                  This is the ID of your chatbot. You will need this ID to
                  connect to the chatbot using integrations.
                </p>
                <Input id="operator-name" defaultValue="Texagon" />
              </div>

              <div className="space-y-2">
                <label htmlFor="default-answer" className="font-medium">
                  Operator Default Answer
                </label>
                <p className="text-sm text-muted-foreground">
                  This answer will be used when the chatbot is unable to find a
                  relevant answer.
                </p>
                <Textarea
                  id="default-answer"
                  rows={3}
                  defaultValue="I am sorry, I do not know the answer to that question. Please contact support for further assistance."
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="default-language" className="font-medium">
                  Operator Default Language
                </label>
                <p className="text-sm text-muted-foreground">
                  This language will be used when the chatbot replies to a user.
                  If set to auto, the chatbot will attempt to automatically
                  detect the language.
                </p>
                <Select defaultValue="auto">
                  <SelectTrigger
                    id="default-language"
                    className="w-full sm:w-[200px]"
                  >
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="it">Italian</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label htmlFor="ai-model" className="font-medium">
                  AI Model of Operator
                </label>
                <p className="text-sm text-muted-foreground">
                  This AI model will be used to generate answers for your
                  chatbot.
                </p>
                <Select defaultValue="gpt-4o-mini">
                  <SelectTrigger id="ai-model" className="w-full sm:w-[200px]">
                    <SelectValue placeholder="Select model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4o-mini">gpt-4o-mini</SelectItem>
                    <SelectItem value="gpt-4o">gpt-4o</SelectItem>
                    <SelectItem value="gpt-4">gpt-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">gpt-3.5-turbo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-start pt-4">
              <Button className="bg-primary hover:bg-orange-600">Update</Button>
            </div>
          </TabsContent>

          <TabsContent value="escalation" className="pt-6 space-y-6">
            <div className="space-y-1">
              <h2 className="text-lg font-semibold">Escalations Settings</h2>
              <p className="text-sm text-muted-foreground">
                These settings control how your visitors can escalate to human
                support.
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="escalation-thresholds" className="font-medium">
                  Escalation Thresholds
                </label>
                <p className="text-sm text-muted-foreground">
                  Define conditions for transferring to human agents (e.g.,
                  unresolved queries after 3 attempts, negative sentiment score)
                </p>
                <Textarea
                  id="escalation-thresholds"
                  rows={3}
                  placeholder="Enter escalation conditions..."
                />
              </div>

              <div className="space-y-4">
                <label className="font-medium">Context Sharing</label>

                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    Sharing customer interaction history
                  </span>
                  <Switch />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm">context during handovers</span>
                  <Switch />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="priority-routing" className="font-medium">
                  Priority Routing Rules
                </label>
                <p className="text-sm text-muted-foreground">
                  Set rules for VIP customers or urgent issues
                </p>
                <Select defaultValue="direct">
                  <SelectTrigger
                    id="priority-routing"
                    className="w-full sm:w-[280px]"
                  >
                    <SelectValue placeholder="Select routing method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="direct">
                      direct human agent routing
                    </SelectItem>
                    <SelectItem value="priority-queue">
                      priority queue
                    </SelectItem>
                    <SelectItem value="skill-based">
                      skill-based routing
                    </SelectItem>
                    <SelectItem value="round-robin">round-robin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-start pt-4">
              <Button className="bg-primary hover:bg-orange-600">
                Update
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="scheduling" className="pt-6 space-y-6">
            <div className="space-y-1">
              <h2 className="text-lg font-semibold">
                Default Calendar Integration
              </h2>
              <p className="text-sm text-muted-foreground">
                Select primary scheduling system
              </p>
            </div>

            <div className="space-y-6">
              <div>
                <Select defaultValue="google">
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="Select calendar" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="google">Google Calendar</SelectItem>
                    {/* <SelectItem value="outlook">Outlook Calendar</SelectItem>
                    <SelectItem value="apple">Apple Calendar</SelectItem>
                    <SelectItem value="zoom">Zoom Scheduler</SelectItem> */}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <h2 className="text-lg font-semibold">Business Hours</h2>
                <p className="text-sm text-muted-foreground">
                  Configure working hours, holidays, and time zones
                </p>

                <div className="space-y-4 mt-4">
                  {/* Sunday */}
                  <div className="flex items-center gap-4">
                    <div className="w-24">Sunday</div>
                    <div className="flex items-center gap-2">
                      <Switch id="sunday" />
                      <span className="text-sm text-muted-foreground">
                        Closed
                      </span>
                    </div>
                  </div>

                  {/* Monday */}
                  <div className="flex items-center gap-4">
                    <div className="w-24">Monday</div>
                    <div className="flex items-center gap-2">
                      <Switch id="monday" defaultChecked />
                      <span className="text-sm">Open</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select defaultValue="9:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="Start time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="8:00">8:00 AM</SelectItem>
                          <SelectItem value="8:30">8:30 AM</SelectItem>
                          <SelectItem value="9:00">9:00 AM</SelectItem>
                          <SelectItem value="9:30">9:30 AM</SelectItem>
                          <SelectItem value="10:00">10:00 AM</SelectItem>
                        </SelectContent>
                      </Select>
                      <span>To</span>
                      <Select defaultValue="17:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="End time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="16:00">4:00 PM</SelectItem>
                          <SelectItem value="16:30">4:30 PM</SelectItem>
                          <SelectItem value="17:00">5:00 PM</SelectItem>
                          <SelectItem value="17:30">5:30 PM</SelectItem>
                          <SelectItem value="18:00">6:00 PM</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Tuesday */}
                  <div className="flex items-center gap-4">
                    <div className="w-24">Tuesday</div>
                    <div className="flex items-center gap-2">
                      <Switch id="tuesday" defaultChecked />
                      <span className="text-sm">Open</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select defaultValue="9:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="Start time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="8:00">8:00 AM</SelectItem>
                          <SelectItem value="8:30">8:30 AM</SelectItem>
                          <SelectItem value="9:00">9:00 AM</SelectItem>
                          <SelectItem value="9:30">9:30 AM</SelectItem>
                          <SelectItem value="10:00">10:00 AM</SelectItem>
                        </SelectContent>
                      </Select>
                      <span>To</span>
                      <Select defaultValue="17:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="End time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="16:00">4:00 PM</SelectItem>
                          <SelectItem value="16:30">4:30 PM</SelectItem>
                          <SelectItem value="17:00">5:00 PM</SelectItem>
                          <SelectItem value="17:30">5:30 PM</SelectItem>
                          <SelectItem value="18:00">6:00 PM</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Wednesday */}
                  <div className="flex items-center gap-4">
                    <div className="w-24">Wednesday</div>
                    <div className="flex items-center gap-2">
                      <Switch id="wednesday" defaultChecked />
                      <span className="text-sm">Open</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select defaultValue="9:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="Start time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="8:00">8:00 AM</SelectItem>
                          <SelectItem value="8:30">8:30 AM</SelectItem>
                          <SelectItem value="9:00">9:00 AM</SelectItem>
                          <SelectItem value="9:30">9:30 AM</SelectItem>
                          <SelectItem value="10:00">10:00 AM</SelectItem>
                        </SelectContent>
                      </Select>
                      <span>To</span>
                      <Select defaultValue="17:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="End time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="16:00">4:00 PM</SelectItem>
                          <SelectItem value="16:30">4:30 PM</SelectItem>
                          <SelectItem value="17:00">5:00 PM</SelectItem>
                          <SelectItem value="17:30">5:30 PM</SelectItem>
                          <SelectItem value="18:00">6:00 PM</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Thursday */}
                  <div className="flex items-center gap-4">
                    <div className="w-24">Thursday</div>
                    <div className="flex items-center gap-2">
                      <Switch id="thursday" defaultChecked />
                      <span className="text-sm">Open</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select defaultValue="9:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="Start time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="8:00">8:00 AM</SelectItem>
                          <SelectItem value="8:30">8:30 AM</SelectItem>
                          <SelectItem value="9:00">9:00 AM</SelectItem>
                          <SelectItem value="9:30">9:30 AM</SelectItem>
                          <SelectItem value="10:00">10:00 AM</SelectItem>
                        </SelectContent>
                      </Select>
                      <span>To</span>
                      <Select defaultValue="17:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="End time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="16:00">4:00 PM</SelectItem>
                          <SelectItem value="16:30">4:30 PM</SelectItem>
                          <SelectItem value="17:00">5:00 PM</SelectItem>
                          <SelectItem value="17:30">5:30 PM</SelectItem>
                          <SelectItem value="18:00">6:00 PM</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Friday */}
                  <div className="flex items-center gap-4">
                    <div className="w-24">Friday</div>
                    <div className="flex items-center gap-2">
                      <Switch id="friday" defaultChecked />
                      <span className="text-sm">Open</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select defaultValue="9:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="Start time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="8:00">8:00 AM</SelectItem>
                          <SelectItem value="8:30">8:30 AM</SelectItem>
                          <SelectItem value="9:00">9:00 AM</SelectItem>
                          <SelectItem value="9:30">9:30 AM</SelectItem>
                          <SelectItem value="10:00">10:00 AM</SelectItem>
                        </SelectContent>
                      </Select>
                      <span>To</span>
                      <Select defaultValue="17:00">
                        <SelectTrigger className="w-[100px]">
                          <SelectValue placeholder="End time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="16:00">4:00 PM</SelectItem>
                          <SelectItem value="16:30">4:30 PM</SelectItem>
                          <SelectItem value="17:00">5:00 PM</SelectItem>
                          <SelectItem value="17:30">5:30 PM</SelectItem>
                          <SelectItem value="18:00">6:00 PM</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Saturday */}
                  <div className="flex items-center gap-4">
                    <div className="w-24">Saturday</div>
                    <div className="flex items-center gap-2">
                      <Switch id="saturday" />
                      <span className="text-sm text-muted-foreground">
                        Closed
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h2 className="text-lg font-semibold">Buffer Time</h2>
                <p className="text-sm text-muted-foreground">
                  Set minimum gap between appointments
                </p>
                <Select defaultValue="60">
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="Select buffer time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 Minutes</SelectItem>
                    <SelectItem value="30">30 Minutes</SelectItem>
                    <SelectItem value="60">1 Hour</SelectItem>
                    <SelectItem value="90">1.5 Hours</SelectItem>
                    <SelectItem value="120">2 Hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <h2 className="text-lg font-semibold">Cancellation Policy</h2>
                <p className="text-sm text-muted-foreground">
                  E.g., cancellation window, fees
                </p>
                <Textarea
                  rows={3}
                  defaultValue="I am sorry, I do not know the answer to that question. Please contact support for further assistance."
                />
              </div>
            </div>

            <div className="flex justify-start pt-4">
              <Button className="bg-primary hover:bg-orange-600">Update</Button>
            </div>
          </TabsContent>

          <TabsContent value="delete" className="pt-6 space-y-6">
            <div className="space-y-1">
              <h2 className="text-lg font-semibold">Delete Chatbot</h2>
              <p className="text-sm text-muted-foreground">
                Delete the chatbot and all associated data. This action cannot
                be undone.
              </p>
             
            </div>

            <div className="py-8 flex justify-start">
              <Button
                variant="destructive"
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={handleDeleteClick}
                disabled={!voiceAgentId || isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Chatbot"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <div className="mt-8 text-center text-sm text-muted-foreground">
        Powered by <span className="font-semibold">Operator AI</span>
      </div>
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to delete this chatbot?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete the chatbot &quot;
              {agentName || "Unknown"}&quot; and all its associated data. This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
