"use client";
import React, { useState, useRef } from "react";
import { X, Music, Upload, Loader2, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import useAgentStore from "@/store/features/useAgentStore";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useUploadAudioFilesMutation } from "@/store/features/trainingApi";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SerializedError } from "@reduxjs/toolkit";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

interface AudioUploadTabProps {
  title: string;
  description: string;
  acceptTypes: string;
  uploadButtonText?: string;
  onUploadSuccess?: () => void;
}

export function AudioUploadTab({
  title,
  description,
  acceptTypes,
  uploadButtonText = "Upload Files",
  onUploadSuccess,
}: AudioUploadTabProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [transcriptionService, setTranscriptionService] =
    useState<string>("openai");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [transcriptionResults, setTranscriptionResults] = useState<
    {
      fileName: string;
      transcription: string;
    }[]
  >([]);

  // Get voice agent ID from store
  const voiceAgentId = useAgentStore((state) => state.voiceAgentId);

  // Upload files mutation
  const [uploadAudioFiles] = useUploadAudioFilesMutation();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);

      // Filter for supported audio file types
      const supportedFiles = files.filter((file) => {
        const ext = file.name.split(".").pop()?.toLowerCase();
        return ["mp3", "mp4", "mpeg", "mpga", "m4a", "wav", "webm"].some(
          (supportedExt) => supportedExt === ext
        );
      });

      // Check file sizes (max 25MB)
      const validSizeFiles = supportedFiles.filter((file) => {
        if (file.size > 25 * 1024 * 1024) {
          toast.error(`File ${file.name} exceeds the 25MB limit.`);
          return false;
        }
        return true;
      });

      if (supportedFiles.length < files.length) {
        toast.warning(
          "Some files were skipped because they are not supported audio formats."
        );
      }

      setSelectedFiles((prev) => [...prev, ...validSizeFiles]);
    }
  };

  const handleClearFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleClearAllFiles = () => {
    setSelectedFiles([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUploadClick = async () => {
    if (!voiceAgentId) {
      toast.error("No voice agent ID found. Please create an agent first.");
      return;
    }

    if (selectedFiles.length === 0) {
      toast.error("Please select at least one audio file to upload.");
      return;
    }

    setIsUploading(true);
    setUploadError(null);
    setTranscriptionResults([]);

    try {
      // Upload files one at a time and collect transcriptions
      for (const file of selectedFiles) {
        const formData = new FormData();
        formData.append("files", file);

        // Using the uploadAudioFiles mutation
        const response = await uploadAudioFiles({
          voiceAgentId,
          files: [file],
          transcriptionService,
        }).unwrap();

        // If we got a transcription back, store it
        if (response && response.transcription) {
          setTranscriptionResults((prev) => [
            ...prev,
            {
              fileName: file.name,
              transcription:
                response.transcription || "No transcription available",
            },
          ]);

          toast.success(`Transcribed ${file.name} successfully!`);
        }
      }

      toast.success("All audio files processed successfully!");

      // Don't clear files automatically so user can see which files match which transcriptions
      // handleClearAllFiles();

      if (onUploadSuccess) {
        onUploadSuccess();
      }
    } catch (error) {
      console.error("Upload error:", error);

      const fetchError = error as FetchBaseQueryError | SerializedError;

      // Handle specific error types
      if ("status" in fetchError && fetchError.status === 413) {
        setUploadError(
          "File too large. Please use smaller audio files (under 15MB recommended)."
        );
        toast.error("File too large. Server rejected the upload.");
      } else {
        const errorMessage =
          "data" in fetchError &&
          typeof fetchError.data === "object" &&
          fetchError.data &&
          "message" in fetchError.data
            ? String(fetchError.data.message)
            : "Unknown error";
        setUploadError(`Failed to upload audio files: ${errorMessage}`);
        toast.error("Failed to upload audio files. Please try again.");
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  };

  // Get audio icon based on file type
  const getAudioIcon = (fileName: string) => {
    const ext = fileName.split(".").pop()?.toLowerCase();

    // Return different icon based on file type
    if (ext === "mp3" || ext === "mpeg" || ext === "mpga") {
      return <Music className="shrink-0 text-purple-600" />;
    } else if (ext === "wav") {
      return <Music className="shrink-0 text-blue-600" />;
    } else {
      return <Music className="shrink-0 text-green-600" />;
    }
  };

  return (
    <div>
      <h3 className="text-lg font-medium">{title}</h3>
      <p className="text-sm text-gray-500 mt-1 mb-4">{description}</p>

      {/* Transcription Service Selection */}
      <div className="mb-4">
        <h4 className="text-sm font-medium mb-2">Transcription Service</h4>
        <RadioGroup
          value={transcriptionService}
          onValueChange={setTranscriptionService}
          className="flex space-x-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="openai" id="openai" />
            <Label htmlFor="openai">OpenAI (General Use)</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="elevenlabs" id="elevenlabs" />
            <Label htmlFor="elevenlabs">ElevenLabs (Multi-speaker)</Label>
          </div>
        </RadioGroup>
        <p className="text-xs text-gray-500 mt-1">
          {transcriptionService === "openai"
            ? "Best for general transcription. Uses gpt-4o-mini-transcribe model."
            : "Better for multi-speaker audio. Supports speaker diarization."}
        </p>
      </div>

      {/* Error message if upload failed */}
      {uploadError && (
        <Alert className="mb-4 bg-red-50 text-red-800 border-red-200">
          <AlertDescription>{uploadError}</AlertDescription>
        </Alert>
      )}

      <div className="mb-6">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
          <input
            ref={fileInputRef}
            type="file"
            accept={acceptTypes}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />
          <button
            onClick={handleAttachClick}
            className="w-full h-full flex flex-col items-center justify-center cursor-pointer"
          >
            <Upload className="h-10 w-10 text-gray-400 mb-2" />
            <span className="font-medium">Click to upload audio files</span>
            <span className="text-sm text-gray-500">
              or drag and drop files here
            </span>
            <span className="text-xs text-gray-400 mt-2">
              Supported formats: MP3, MP4, MPEG, MPGA, M4A, WAV, WEBM (Max size:
              25MB)
            </span>
          </button>
        </div>

        {/* Selected files list */}
        {selectedFiles.length > 0 && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-md font-medium">
                Selected Files ({selectedFiles.length})
              </h4>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAllFiles}
                className="text-xs text-red-500"
              >
                Remove All
              </Button>
            </div>

            <div className="space-y-2 max-h-60 overflow-y-auto pr-2 rounded-lg border border-gray-100 p-2 bg-gray-50">
              {selectedFiles.map((file, index) => {
                // Check if this file has a transcription result
                const hasTranscription = transcriptionResults.some(
                  (result) => result.fileName === file.name
                );

                return (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      hasTranscription
                        ? "border-green-200 bg-green-50"
                        : "border-purple-200 bg-white"
                    } shadow-sm`}
                  >
                    <div className="flex items-center gap-3 overflow-hidden">
                      {hasTranscription ? (
                        <CheckCircle className="shrink-0 text-green-600" />
                      ) : (
                        getAudioIcon(file.name)
                      )}
                      <div className="truncate text-sm">
                        <p className="font-medium truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(file.size)}
                          {hasTranscription && " • Transcribed"}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full text-gray-400 hover:text-red-500"
                      onClick={() => handleClearFile(index)}
                    >
                      <X size={16} />
                    </Button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        <Button
          onClick={handleUploadClick}
          disabled={selectedFiles.length === 0 || isUploading}
          className="w-full mt-4 bg-orange-500 hover:bg-orange-600 text-white"
        >
          {isUploading ? (
            <div className="flex items-center">
              <Loader2 className="animate-spin mr-2 h-4 w-4" />
              Uploading and Transcribing...
            </div>
          ) : (
            uploadButtonText
          )}
        </Button>

        {/* Transcription results */}
        {transcriptionResults.length > 0 && (
          <div className="mt-6">
            <h4 className="text-md font-medium mb-2">Transcription Results</h4>
            <div className="space-y-4">
              {transcriptionResults.map((result, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardHeader className="bg-gray-50 py-3">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      {getAudioIcon(result.fileName)}
                      {result.fileName}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <p className="text-sm whitespace-pre-line">
                      {result.transcription}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
