import Image from "next/image";
import { But<PERSON> } from "../ui/button";
import gfeature from "../../../public/images/gfeature.png";
import gfeature2 from "../../../public/images/gfeature2.png";
import gfeature3 from "../../../public/images/gfeature3.png";
import gfeature4 from "../../../public/images/gfeature4.png";
import gfeature5 from "../../../public/images/gfeature5.png";
import gfeature6 from "../../../public/images/gfeature6.png";

const FeatureSection = () => {
	return (
    <section id="features" className="container mx-auto px-6 py-24 md:mt-[68rem] lg:mt-[68rem]  xl:mt-[74rem] 2xl:mt-[94rem]  sm:mt-[60rem] mt-[32rem]">
      {/* Header */}
      <div className="text-center mb-16">
        <Button
          variant="default"
          className="bg-orange-500 text-white mb-8 uppercase"
        >
          Features
        </Button>
        <h2 className="text-4xl font-bold text-gray-900">
          How we&apos;re different
        </h2>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {/* NLP Card */}
        <div className="rounded-2xl p-8 relative overflow-hidden ">
          <div className="relative z-10 2xl:min-h-[300px] 2xl:min-w-[300px] p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              (NLP) and predictive
            </h3>
            <p className="text-gray-700">
              This feature significantly reduces response delays, improving
              customer satisfaction and retention while maintaining a high level
              of service quality.
            </p>
          </div>
          <div className="absolute bottom-0 ">
            <Image
              src={gfeature}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-cover rounded-2xl"
              priority
            />
          </div>
        </div>

        {/* Reduce HR Cost Card */}

        <div className="rounded-2xl p-8 relative overflow-hidden ">
          <div className="relative z-10 min-h-[300px] p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Reduce HR Cost
            </h3>
            <p className="text-gray-700 w-[40%]">
              Automatically directs complex issues to the appropriate human
              agent, optimizing team efficiency.
            </p>
          </div>
          <div className="absolute bottom-0 ">
            <Image
              src={gfeature2}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-cover rounded-2xl"
              priority
            />
          </div>
        </div>

        {/* Centralized Management Card */}

        <div className="rounded-2xl p-8 relative overflow-hidden ">
          <div className="relative z-10 min-h-[300px] p-6 ">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 mt-24">
              <span className="block">Centralized </span>Management System
            </h3>
            <p className="text-gray-700 w-[95%]">
              Seamlessly switch between channels without losing context,
              ensuring a consistent and efficient customer experience
            </p>
          </div>
          <div className="absolute bottom-0 ">
            <Image
              src={gfeature3}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-coverrounded-2xl"
              priority
            />
          </div>
        </div>

        {/* Breaks Language Barriers Card */}

        <div className="rounded-2xl p-8 relative overflow-hidden ">
          <div className="relative z-10 min-h-[300px] p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              <span className="block"> Breaks Language </span> Barriers
            </h3>
            <p className="text-gray-700 w-[82%]">
              Enabling customers to <br /> communicate effortlessly in their{" "}
              <br />
              preferred language by translating <br /> text and voice
              interactions instantly
            </p>
          </div>
          <div className="absolute bottom-0 ">
            <Image
              src={gfeature4}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-cover rounded-2xl"
              priority
            />
          </div>
        </div>

        {/* Customer Satisfaction Card */}

        <div className="rounded-2xl p-8 relative overflow-hidden ">
          <div className="relative z-10 min-h-[300px] p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 mt-48">
              Customer Satisfaction
            </h3>
            <p className="text-gray-700 w-[95%]">
              Utilize AI to analyze customer preferences and history, offering
              tailored solutions and recommendations
            </p>
          </div>
          <div className="absolute bottom-0 ">
            <Image
              src={gfeature5}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-cover rounded-2xl"
              priority
            />
          </div>
        </div>

        {/* Businesses to Expand Card */}

        <div className="rounded-2xl p-8 relative overflow-hidden ">
          <div className="relative z-10 min-h-[300px] p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Businesses to Expand
            </h3>
            <p className="text-gray-700 ">
              By analyzing customer behavior, market trends, and operational
              data, the system provides actionable insights and personalized
              recommendations tailored to each business&apos;s unique needs.
            </p>
          </div>
          <div className="absolute bottom-0 ">
            <Image
              src={gfeature6}
              alt="Background Pattern"
              width={1000}
              height={1800}
              className="w-full h-[400px] object-cover rounded-2xl"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureSection;