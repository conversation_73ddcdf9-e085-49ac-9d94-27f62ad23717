import Image from "next/image";
import React from "react";
import logo from "../../public/images/verbal.webp";
import poweredby from "../../public/images/powerby.png";


const GeneralLoadingScreen = () => {
  const [progress, setProgress] = React.useState(0);
  const [loadingPhase, setLoadingPhase] = React.useState(0);

  React.useEffect(() => {
    const progressTimer = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress >= 100) {
          clearInterval(progressTimer);
          return 100;
        }
        return prevProgress + 1;
      });
    }, 80);

    const phaseTimer = setInterval(() => {
      setLoadingPhase((prev) => (prev + 1) % loadingPhases.length);
    }, 2500);

    return () => {
      clearInterval(progressTimer);
      clearInterval(phaseTimer);
    };
  }, []);

  const loadingPhases = [
    "Initializing application...",
    "Loading resources...",
    "Configuring voice agent...",
    "Preparing interface...",
  ];

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-gradient-to-b from-[#fdede7] to-[#f7c8b6]">
      <div className="w-24 h-24 mb-8 bg-white rounded-full p-2 shadow-lg flex items-center justify-center">
        {/* Placeholder for company logo */}
        <Image
          src={logo}
          alt="Company Logo"
          width={96}
          height={96}
          className="w-full h-full object-contain rounded-full"
        />
      </div>

      <h1 className="text-2xl font-bold text-[#e64f13] mb-1">Voice Agent</h1>
      <p className="text-[#b83f0f] mb-6 font-medium">
        Intelligent conversation platform
      </p>

      <div className="w-72 mb-8">
        <div className="flex items-center justify-center mb-3">
          <div className="h-4 w-4 relative mr-3">
            <span className="absolute animate-ping w-full h-full rounded-full bg-[#e64f13] opacity-30"></span>
            <span className="absolute w-full h-full rounded-full bg-[#e64f13]"></span>
          </div>
          <span className="text-[#ad3b0e] font-medium">
            {loadingPhases[loadingPhase]}
          </span>
        </div>

        <div className="w-full bg-white rounded-full h-3 mb-2 overflow-hidden shadow-inner">
          <div
            className="bg-gradient-to-r from-[#e64f13] to-[#f7c8b6] h-full rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>

        <div className="flex justify-between items-center w-full">
          <span className="text-sm text-[#ad3b0e]">{progress}% complete</span>
          {progress < 100 ? (
            <span className="text-xs px-2 py-0.5 bg-[#f7c8b6] text-[#ad3b0e] rounded-full">
              Please wait...
            </span>
          ) : (
            <span className="text-xs px-2 py-0.5 bg-[#b83f0f] text-white rounded-full animate-pulse">
              Ready!
            </span>
          )}
        </div>
      </div>

      <div className="flex flex-wrap justify-center gap-2 max-w-xs">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="w-3 h-3 rounded-full bg-[#e64f13]"
            style={{
              opacity: 0.3 + (i / 4) * 0.7,
              animationDelay: `${i * 200}ms`,
              animation: "pulse 1.5s infinite",
            }}
          />
        ))}
      </div>

      <style jsx>{`
        @keyframes pulse {
          0%,
          100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.2);
          }
        }
      `}</style>

      <div className="absolute bottom-4 text-xs text-[#ad3b0e] text-center">
        <p>© 2025 Voice Agent</p>
        <Image
          src={poweredby}
          alt="Powered by Texagon"
          style={{ width: "auto", height: "auto" }}
        />
      </div>
    </div>
  );
};

export default GeneralLoadingScreen;
