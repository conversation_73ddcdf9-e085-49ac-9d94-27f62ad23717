"use client"; // Ensure this runs on the client side
import { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { useAuthStore } from "@/store/features/useAuthStore";
import { User as SupabaseUser } from "@supabase/auth-js";

interface User {
  id: string;
  email: string;
  username: string;
  company?: string;
  phone?: string;
  title?: string;
  name?: string;
  avatar?: string;
}

interface SessionContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (token: string, refreshToken: string, user: User) => void;
  logout: () => Promise<void>;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

// Helper function to transform Supabase User to our User type
const transformUser = (supabaseUser: SupabaseUser): User => {
  // Extract standard fields from Supabase User
  const { id, email } = supabaseUser;

  // Extract custom fields from user_metadata or app_metadata
  // Assuming username and other fields are stored in user_metadata
  const username = supabaseUser.user_metadata?.username || "";
  const company = supabaseUser.user_metadata?.company;
  const phone = supabaseUser.user_metadata?.phone;
  const title = supabaseUser.user_metadata?.title;
  const name = supabaseUser.user_metadata?.name;
  const avatar = supabaseUser.user_metadata?.avatar;

  return {
    id,
    email: email || "",
    username,
    company,
    phone,
    title,
    name,
    avatar,
  };
};

export const SessionProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const { token, refreshToken, user, setAuth, clearAuth } = useAuthStore();
  const router = useRouter();

  // Check for an existing session on initial load
  useEffect(() => {
    const checkSession = async () => {
      setIsLoading(true);

      // If there's a token in the store, check if it's still valid
      if (token && refreshToken) {
        try {
          // Verify if the token is still valid by making a lightweight request
          const { data, error } = await supabase.auth.getUser(token);

          if (error || !data.user) {
            // Token is invalid, try to refresh it
            const { data: refreshData, error: refreshError } =
              await supabase.auth.refreshSession({
                refresh_token: refreshToken,
              });

            if (refreshError || !refreshData.session) {
              // Refresh failed, clear auth and redirect to login
              console.warn(
                "Failed to refresh invalid token:",
                refreshError?.message
              );
              clearAuth();
              router.push("/signin");
            } else {
              // Token refreshed successfully
              const transformedUser = transformUser(refreshData.session.user);
              setAuth(
                refreshData.session.access_token,
                refreshData.session.refresh_token,
                transformedUser
              );
            }
          }
        } catch (error) {
          console.error("Error verifying token:", error);
        }

        setIsLoading(false);
        return;
      }

      // No token in store, check Supabase for active session
      try {
        const {
          data: { session },
         
        } = await supabase.auth.getSession();

        if (session) {
          // We have a valid session, set it in our store
          const transformedUser = transformUser(session.user);
          setAuth(session.access_token, session.refresh_token, transformedUser);
        }
      } catch (error) {
        console.error("Error getting session:", error);
      }

      setIsLoading(false);
    };

    checkSession();
  }, [token, refreshToken, setAuth, clearAuth, router]);

  // Set up Supabase auth state change listener
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === "SIGNED_IN" && session) {
        console.log("Auth state changed: signed in");
        const transformedUser = transformUser(session.user);
        setAuth(session.access_token, session.refresh_token, transformedUser);
      } else if (event === "SIGNED_OUT") {
        console.log("Auth state changed: signed out");
        clearAuth();
        router.push("/signin");
      } else if (event === "TOKEN_REFRESHED" && session) {
        console.log("Auth state changed: token refreshed");
        const transformedUser = transformUser(session.user);
        setAuth(session.access_token, session.refresh_token, transformedUser);
      }
    });

    // Clean up subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [setAuth, clearAuth, router]);

  // Setup automatic token refresh
  useEffect(() => {
    if (!token || !refreshToken) return;

    // Calculate when to refresh the token
    // Typically you should refresh before expiration
    // JWT tokens usually have an exp claim we could decode
    // For simplicity, we're using a 50-minute interval (assuming 1-hour tokens)
    const refreshIntervalMs = 50 * 60 * 1000;

    const refreshTimerId = setInterval(async () => {
      console.log("Attempting to refresh session token");

      try {
        const { data, error } = await supabase.auth.refreshSession({
          refresh_token: refreshToken,
        });

        if (error || !data.session) {
          console.warn("Failed to refresh session:", error?.message);
          await logout();
        } else {
          console.log("Session refreshed successfully");
          const transformedUser = transformUser(data.session.user);
          setAuth(
            data.session.access_token,
            data.session.refresh_token,
            transformedUser
          );
        }
      } catch (err) {
        console.error("Error refreshing token:", err);
        await logout();
      }
    }, refreshIntervalMs);

    // Clean up the interval when component unmounts or token changes
    return () => clearInterval(refreshTimerId);
  }, [token, refreshToken, setAuth]);

  // Handle login
  const login = (token: string, refreshToken: string, user: User) => {
    setAuth(token, refreshToken, user);
    router.push("/dashboard"); // Redirect after login
  };

  // Handle logout
  const logout = async () => {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error("Error signing out from Supabase:", error);
    }
    clearAuth();
    router.push("/signin"); // Redirect after logout
  };

  return (
    <SessionContext.Provider
      value={{
        isAuthenticated: !!token,
        user,
        isLoading,
        login,
        logout,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
};

// Custom hook to use the session context
export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error("useSession must be used within a SessionProvider");
  }
  return context;
};
