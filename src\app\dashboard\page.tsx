"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Dashboard from "./components/dashboard";
import useAgentStore from "@/store/features/useAgentStore";
import { useGetVoiceAgentsQuery } from "@/store/features/voiceAgentApi";
import GeneralLoadingScreen from "@/components/GeneralLoadingScreen";


const Page = () => {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const {
    data: voiceAgents,
    isLoading: isLoadingAgents,
    isError,
    error,
  } = useGetVoiceAgentsQuery();

  const { voiceAgentId, setVoiceAgentId, setAgentName } = useAgentStore();

  useEffect(() => {
    if (!isLoadingAgents && !isRedirecting) {
      if (isError) {
        console.error("Error fetching voice agents:", error);

        setIsRedirecting(true);
        router.push("/create-agent?error=api");
        return;
      }

      if (voiceAgents && voiceAgents.length > 0) {
        const agentExists =
          voiceAgentId &&
          voiceAgentId !== "" &&
          voiceAgents.some((agent) => agent.id === voiceAgentId);

        if (!agentExists) {
          // Only set a default if no valid agent is already selected
          const firstAgent = voiceAgents[0];
          console.log(
            "Setting default agent because no valid agent was selected:",
            firstAgent.name
          );
          setVoiceAgentId(firstAgent.id);
          setAgentName(firstAgent.name);
        } else {
          // If current ID exists, find that agent for logging
          const currentAgent = voiceAgents.find(
            (agent) => agent.id === voiceAgentId
          );
          console.log("Found voice agent:", currentAgent);
        }
      } else {
        // No voice agents found for this user
        console.log("No voice agents found, redirecting to create-agent");
        setIsRedirecting(true);
        router.push("/create-agent");
      }
    }
  }, [
    voiceAgents,
    isLoadingAgents,
    isError,
    router,
    voiceAgentId,
    setVoiceAgentId,
    setAgentName,
    isRedirecting,
  ]);

  // Show loading state while API is fetching
  if (isLoadingAgents) {
    return (
      <div>
        <GeneralLoadingScreen />
      </div>
    );
  }

  // If we have voice agents, show the dashboard
  if (voiceAgents && voiceAgents.length > 0) {
    return <Dashboard />;
  }

  // Fallback (should rarely hit this)
  return <div>Checking voice agent status...</div>;
};

export default Page;
