import React from "react";
import Image from "next/image";
import logo from "../../../../public/images/verbal.webp";

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
}

const Logo = ({ className = "", width = 100, height = 100 }: LogoProps) => {
  return (
    <div className={`flex items-center ${className}`}>
      <div className="relative">
        <Image
          src={logo}
          alt="Operator AI Logo"
          width={width}
          height={height}
          className="object-contain"
          priority
        />
      </div>
    </div>
  );
};

export default Logo;
