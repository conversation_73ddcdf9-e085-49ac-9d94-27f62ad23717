// middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { supabase } from "@/lib/supabase";

// Define public and protected routes
const PUBLIC_ROUTES = ["/signin", "/signup", "/reset-password"];
const PROTECTED_ROUTES = ["/dashboard", "/profile"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Redirect logic for protected routes
  if (PROTECTED_ROUTES.some((route) => pathname.startsWith(route))) {
    if (!session) {
      // Redirect to sign-in page if not authenticated
      return NextResponse.redirect(new URL("/signin", request.url));
    }
  }

  // Redirect logic for public routes
  if (PUBLIC_ROUTES.includes(pathname)) {
    if (session) {
      // Redirect to dashboard if already authenticated
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
  }

  // Allow the request to proceed
  return NextResponse.next();
}

// Define which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api routes (API routes)
     */
    "/((?!_next/static|_next/image|favicon.ico|api).*)",
  ],
};
