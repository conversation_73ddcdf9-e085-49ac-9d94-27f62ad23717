"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useResetPasswordMutation } from "@/store/features/authApi";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import AuthLayout from "../components/auth-layout";

interface ResetPasswordError {
  message?: string;
  data?: {
    message?: string;
  };
  status?: number;
}

export default function ForgotPasswordForm() {
  const [email, setEmail] = useState("");
  const [resetPassword, { isLoading }] = useResetPasswordMutation();
  const { toast } = useToast();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await resetPassword(email).unwrap();
      router.push(`/reset-password/confirm?email=${encodeURIComponent(email)}`);
    } catch (error: unknown) {
      const resetError = error as ResetPasswordError;

      toast({
        description:
          resetError.message ||
          resetError.data?.message ||
          "Failed to reset password",
        variant: "destructive",
      });
    }
  };

  return (
    <AuthLayout
      heading="Forgot Password"
      subheading="Enter your email to reset password"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="h-12 border-gray-200 focus:border-[#E84B1C] focus:ring-2 focus:ring-[#E84B1C] focus:ring-opacity-20"
          />
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-[#E84B1C] hover:bg-[#E84B1C]/90 text-white"
          disabled={isLoading}
        >
          Reset Password
        </Button>
      </form>
    </AuthLayout>
  );
}
