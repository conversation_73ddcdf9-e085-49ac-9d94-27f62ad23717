"use client";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { useToast } from "@/hooks/use-toast";
import AuthLayout from "../components/auth-layout";
import { useSignUpMutation } from "@/store/features/authApi";
import { useAuthStore } from "@/store/features/useAuthStore";

interface ApiError {
  data?: {
    message?: string;
  };
  message?: string;
  status?: number;
}

export default function InitialSignUpForm() {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
  });
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [signUp, { isLoading }] = useSignUpMutation();
  const setAuth = useAuthStore((state) => state.setAuth);
  const { toast } = useToast();
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  if (!agreedToTerms) {
    toast({
      description: "Please agree to the Terms of Service and Privacy Policy",
      variant: "destructive",
    });
    return;
  }

  try {
    // Transform the data to match the API expectations
    const signUpData = {
      email: formData.email,
      password: formData.password,
      username: formData.fullName, // API expects username instead of fullName
    };

    const response = await signUp(signUpData).unwrap();

    // Extract tokens from the response
    const token = response.access_token || response.token;
    const refreshToken = response.refresh_token || "";

    if (!token) {
      throw new Error("No token received from server");
    }

    // Parse JWT to get user data
    const parseJwt = (token: string) => {
      try {
        const base64Url = token.split(".")[1];
        const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
        const jsonPayload = decodeURIComponent(
          atob(base64)
            .split("")
            .map((c) => {
              return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
            })
            .join("")
        );
        return JSON.parse(jsonPayload);
      } catch (error) {
        console.error("Error parsing JWT:", error);
        return {};
      }
    };

    const decodedToken = parseJwt(token);

    // If response doesn't have user data, extract it from the token
    const userData = response.user || {
      id: decodedToken.sub || "",
      email: decodedToken.email || formData.email,
      username: formData.fullName,
      name: formData.fullName,
    };

    // Store auth data including refresh token in Zustand store
    setAuth(token, refreshToken, userData);

    toast({
      description: "Account created successfully!",
    });

    router.push("/signup/get-started");
  } catch (error: unknown) {
    console.error("Sign up error:", error);
    const apiError = error as ApiError;
    toast({
      description:
        apiError?.data?.message ||
        apiError?.message ||
        "Failed to create account",
      variant: "destructive",
    });
  }
};

  return (
    <AuthLayout
      heading="Create account"
      subheading="Get started with Operator AI"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Google Sign In */}
        <button
          type="button"
          className="w-full flex items-center justify-center gap-2 p-3 border rounded-lg mb-6 bg-[#E9EAEA] hover:bg-gray-100 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 48 48"
            className="w-5 h-5"
          >
            <path
              fill="#FFC107"
              d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
            />
            <path
              fill="#FF3D00"
              d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
            />
            <path
              fill="#4CAF50"
              d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
            />
            <path
              fill="#1976D2"
              d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
            />
          </svg>
          <span className="font-normal">Continue with Google</span>
        </button>

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200"></div>
          </div>
          <div className="relative flex justify-center">
            <span className="bg-white px-4 text-sm text-gray-500">Or</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="fullName">Full name</Label>
          <Input
            id="fullName"
            name="fullName"
            type="text"
            placeholder="Steve Smith"
            value={formData.fullName}
            onChange={handleChange}
            required
            className="h-12"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={handleChange}
            required
            className="h-12 border-gray-200 focus:border-[#E84B1C] focus:ring-2 focus:ring-[#E84B1C] focus:ring-opacity-20"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            placeholder="Enter password"
            value={formData.password}
            onChange={handleChange}
            required
            className="h-12 border-gray-200 focus:border-[#E84B1C] focus:ring-2 focus:ring-[#E84B1C] focus:ring-opacity-20"
          />
        </div>

        <div className="flex items-start space-x-2 mt-4">
          <input
            placeholder="terms&conditions"
            type="checkbox"
            id="terms"
            checked={agreedToTerms}
            onChange={(e) => setAgreedToTerms(e.target.checked)}
            className="mt-1"
          />
          <Label htmlFor="terms" className="text-sm text-gray-600 font-normal">
            By clicking &apos;Create account&apos;, I agree to Operator
            AI&apos;s{" "}
            <Link
              href="/terms"
              className="text-[#E84B1C] hover:text-[#E84B1C]/80"
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              href="/privacy"
              className="text-[#E84B1C] hover:text-[#E84B1C]/80"
            >
              Privacy Policy
            </Link>
            .
          </Label>
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-[#E84B1C] hover:bg-[#E84B1C]/90 text-white mt-4"
          disabled={isLoading}
        >
          {isLoading ? "Creating account..." : "Create account"}
        </Button>

        <p className="text-center text-gray-600 text-sm">
          Already have an account?{" "}
          <Link
            href="/signin"
            className="text-[#E84B1C] hover:text-[#E84B1C]/80"
          >
            Sign in
          </Link>
        </p>
      </form>
    </AuthLayout>
  );
}
