"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import useAgentStore from "@/store/features/useAgentStore";
import { useCreateVoiceAgentMutation } from "@/store/features/voiceAgentApi";

// Define type for the handleNext prop
interface StepOneProps {
  handleNext: (
    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<HTMLFormElement>
  ) => void;
}

// Voice options
const VOICE_OPTIONS = [
  { id: "alloy", name: "Alloy" },
  { id: "ash", name: "Ash" },
  { id: "ballad", name: "Ballad" },
  { id: "coral", name: "<PERSON>" },
  { id: "echo", name: "<PERSON>" },
  { id: "sage", name: "<PERSON>" },
  { id: "shimmer", name: "<PERSON><PERSON>" },
  { id: "verse", name: "Verse" },
];

const StepOne = ({ handleNext }: StepOneProps) => {
  // Get setAgentName and setVoiceAgentId from Zustand store
  const setAgentName = useAgentStore((state) => state.setAgentName);
  const setVoiceAgentId = useAgentStore((state) => state.setVoiceAgentId);
  
  // We need to add a function to store the selected voice
  const setVoice = useAgentStore((state) => state.setVoice || (() => {}));

  // Local state for form handling
  const [localAgentName, setLocalAgentName] = React.useState("");
  const [selectedVoice, setSelectedVoice] = React.useState("alloy"); // Default voice
  const [isNameValid, setIsNameValid] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState("");

  // Import the mutation hook from your API
  const [createVoiceAgent, { isLoading }] = useCreateVoiceAgentMutation();

  // Validate the agent name
  React.useEffect(() => {
    // Simple validation - name must be at least 3 characters
    setIsNameValid(localAgentName.trim().length >= 3);
  }, [localAgentName]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalAgentName(e.target.value);
    if (error) setError(""); // Clear any previous errors
  };

  const handleVoiceChange = (value: string) => {
    setSelectedVoice(value);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!isNameValid) return;

    try {
      setIsSubmitting(true);

      // Call the API to create a voice agent with the selected voice
      const response = await createVoiceAgent({
        name: localAgentName.trim(),
        private_settings: {
          model: {
            voice: selectedVoice
          }
        }
      }).unwrap();

      // Store the voice agent ID, agent name, and voice in Zustand store
      if (response && response.id) {
        setVoiceAgentId(response.id);
        setAgentName(localAgentName.trim()); // Save the agent name to the store
        setVoice(selectedVoice); // Save the selected voice to the store
        console.log("Voice agent created with ID:", response.id, "and voice:", selectedVoice);
      } else {
        throw new Error("Voice agent ID not returned from API");
      }

      // Move to the next step
      handleNext(e);
    } catch (err: unknown) {
      console.error("Failed to create voice agent:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Failed to create voice agent. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full border-none shadow-none">
      <CardHeader>
        <CardTitle className="text-center text-3xl font-bold">
          Let&apos;s Get Started: Name Your Agent
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <p className="text-center text-gray-600 mb-8">
            Give your assistant a name and select a voice that reflects its personality or purpose.
            These settings can be changed anytime.
          </p>
          <div className="space-y-4 max-w-md mx-auto">
            <div className="relative">
              <Input
                placeholder="Enter name here"
                className="w-full h-12 p-4 pl-4 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#E74F13] focus:ring-opacity-20 focus:border-[#E74F13]"
                type="text"
                value={localAgentName}
                onChange={handleNameChange}
                disabled={isLoading || isSubmitting}
              />
              {localAgentName && isNameValid && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M20 6L9 17l-5-5"></path>
                  </svg>
                </div>
              )}
            </div>

            <div>
              <label htmlFor="voice-select" className="block text-sm font-medium text-gray-700 mb-1">
                Select Voice
              </label>
              <Select value={selectedVoice} onValueChange={handleVoiceChange}>
                <SelectTrigger 
                  id="voice-select"
                  className="w-full h-12 border border-gray-200 rounded-lg focus:outline-none focus:ring-0 focus:ring-[#E74F13] focus:ring-opacity-20 focus:border-[#E74F13] focus:border-2"
                >
                  <SelectValue placeholder="Select a voice" />
                </SelectTrigger>
                <SelectContent>
                  {VOICE_OPTIONS.map((voice) => (
                    <SelectItem key={voice.id} value={voice.id}>
                      {voice.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {error && (
              <p className="text-sm text-red-500 text-center">{error}</p>
            )}

            <p className="text-sm text-gray-500 text-center">
              Enter an engaging assistant name and choose a voice
            </p>

            <Button
              className="w-full bg-[#E74F13] hover:bg-[#E74F13]/90 h-12 rounded-lg"
              type="submit"
              disabled={!isNameValid || isLoading || isSubmitting}
            >
              {isLoading || isSubmitting ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Creating...
                </div>
              ) : (
                "Continue"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default StepOne;