import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Paperclip, X, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import useAgentStore from "@/store/features/useAgentStore";
import useTrainingStore from "@/store/features/useTrainingStore";
import { useUploadTrainingFilesMutation } from "@/store/features/trainingApi";

interface PDFDialogProps {
  onClose: () => void;
}

// Define type for the selected file
interface UploadedFile extends File {
  name: string;
}

const PDFDialog: React.FC<PDFDialogProps> = ({ onClose }) => {
  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { toast } = useToast();

  // Get voice agent ID from store
  const voiceAgentId = useAgentStore((state) => state.voiceAgentId);

  // Get training store functions
  const addUploadedFile = useTrainingStore((state) => state.addUploadedFile);

  // RTK Query mutation hook for file training
  const [uploadTrainingFiles] = useUploadTrainingFilesMutation();

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0] as UploadedFile;

      // Check if it's a PDF file
      if (
        file.type === "application/pdf" ||
        file.name.toLowerCase().endsWith(".pdf")
      ) {
        setSelectedFile(file);
      } else {
        toast({
          title: "Invalid file type",
          description: "Only PDF files are accepted",
          variant: "destructive",
        });

        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    }
  };

  // Handle file removal
  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "No file selected",
        description: "Please select a PDF file to upload",
        variant: "destructive",
      });
      return;
    }

    if (!voiceAgentId) {
      toast({
        title: "Error",
        description:
          "No voice agent ID found. Please go back and create an agent first.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(10);

      // Upload file using the RTK Query mutation
      await uploadTrainingFiles({
        voiceAgentId,
        files: [selectedFile],
      }).unwrap();

      setUploadProgress(100);

      // Add file to training store
      addUploadedFile(selectedFile);

      toast({
        title: "Success",
        description: "File uploaded successfully!",
      });

      // After successful upload, close the dialog
      setTimeout(() => {
        if (onClose) onClose();
      }, 1000);
    } catch (error: unknown) {
      console.error("Error uploading file:", error);
      toast({
        title: "Upload failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to upload file. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Simulate progress for better UX
  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (isUploading && uploadProgress < 90) {
      interval = setInterval(() => {
        setUploadProgress((prev) => Math.min(prev + 10, 90));
      }, 500);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isUploading, uploadProgress]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="border rounded-lg p-6 flex flex-col items-center hover:border-[#E74F13] cursor-pointer transition-colors">
          <div className="w-12 h-12 bg-[#E74F13] rounded-full flex items-center justify-center mb-4">
            <FileText className="w-6 h-6 text-white" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Attach PDF</h3>
          <p className="text-gray-500 text-center text-sm">
            Drag and drop your pdf file to upload your document
          </p>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <div className="flex flex-col items-center p-6">
          <div className="w-12 h-12 bg-[#E74F13] rounded-full flex items-center justify-center mb-4">
            <FileText className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Attach your PDF</h2>
          <p className="text-gray-500 text-center mb-6">
            Easily train your chatbot by sharing your pdf
          </p>
          <input
            type="file"
            ref={fileInputRef}
            placeholder="file-upload"
            onChange={handleFileChange}
            className="hidden"
            accept=".pdf,application/pdf"
          />
          <div className="w-full max-w-sm mb-6">
            <div className="relative">
              <div className="w-full h-12 border rounded-lg flex items-center justify-between px-4 bg-white">
                {selectedFile ? (
                  <>
                    <span className="text-sm truncate">
                      {selectedFile.name}
                    </span>
                    <button
                      title="remove-file"
                      onClick={handleRemoveFile}
                      className="text-gray-500 hover:text-gray-700"
                      disabled={isUploading}
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </>
                ) : (
                  <>
                    <span className="text-sm text-gray-400">
                      <Paperclip className="-rotate-45" />
                    </span>
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="text-[#E74F13] text-sm font-medium"
                      disabled={isUploading}
                    >
                      Attach here
                    </button>
                  </>
                )}
              </div>
              <span className="text-sm text-gray-500 mt-1 block">
                Upload or drag your file here
              </span>
            </div>

            {/* Progress indicator */}
            {isUploading && (
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading file...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-[#E74F13] transition-all duration-300 ease-in-out"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          <Button
            className="w-full bg-[#E74F13] hover:bg-[#E74F13]/90 rounded-full h-12"
            onClick={handleUpload}
            disabled={!selectedFile || isUploading}
          >
            {isUploading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Uploading...
              </div>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PDFDialog;
