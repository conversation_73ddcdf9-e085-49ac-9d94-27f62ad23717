import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import pro from "../../public/images/pro.png";
import logo from "../../public/images/verbal.webp";

export function OperatorAIPromoCard() {
  return (
    <Card className="w-64 h-[248px] rounded-xl overflow-hidden relative">
      {/* Background gradient */}
      <Image
        src={pro}
        alt="Background"
        fill
        className="object-cover"
        sizes="(max-width: 768px) 100vw, 256px"
        priority
      />

      <CardContent className="relative z-10 flex flex-col items-center justify-between h-full p-6 text-white">
        {/* Logo circle with icon */}
        <div className="flex flex-col items-center mt-0">
          <div className="w-16 h-16 rounded-xl bg-white flex items-center justify-center mb-4">
            <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
              {/* You can replace this with your actual logo SVG */}
              <Image src={logo} alt="Background" />
            </div>
          </div>

          <CardTitle className="text-xl font-bold text-center mb-1 hidden"></CardTitle>

          <CardDescription className="text-white/90 text-center font-thin pb-4 text-sm">
            Get access to all
            <br />
            features on tetumbas
          </CardDescription>
        </div>

        {/* Button */}
        <Button
          className="w-full bg-white hover:bg-white/90 text-orange-600 shadow-none font-extrabold"
          size="lg"
        >
          Get Pro
        </Button>
      </CardContent>
    </Card>
  );
}
