"use client";


import { Radial<PERSON><PERSON>, Ra<PERSON><PERSON>ar<PERSON>hart } from "recharts";

import {
  Card,
  CardContent,

} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const chartData = [
  { browser: "Loyal Customers", visitors: 275, fill: "#F76F39" },
  { browser: "New Customers", visitors: 200, fill: "#E74F13" },
  { browser: "Unique Customers", visitors: 187, fill: "#9A350D" },
];

const chartConfig = {
  "Loyal Customers": {
    label: "Loyal Customers",
    color: "#F76F39",
  },
  "New Customers": {
    label: "New Customers",
    color: "#E74F13",
  },
  "Unique Customers": {
    label: "Unique Customers",
    color: "#9A350D",
  },
} satisfies ChartConfig;

export function RadialChart() {
  return (
    <Card className="flex flex-col border-0 shadow-none">
     
      <CardContent className="flex-1 pb-0">
        <ChartContainer config={chartConfig} className="mx-auto aspect-square">
          <RadialBarChart data={chartData} innerRadius={50} outerRadius={110}>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent nameKey="browser" />}
            />
            <RadialBar dataKey="visitors" background />
          </RadialBarChart>
        </ChartContainer>
      </CardContent>
   
    </Card>
  );
}
