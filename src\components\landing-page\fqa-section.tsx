import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState } from "react";

export default function FAQSection() {
  const [activeItem, setActiveItem] = useState("01");

  const faqs = [
    {
      id: "01",
      question: "How does the AI voice bot work?",
      answer:
        "The bot uses natural language processing (NLP) to understand spoken language, processes the information, and responds in a human-like voice.",
    },
    {
      id: "02",
      question: "What tasks can the AI voice bot handle?",
      answer:
        "The AI voice bot can handle various tasks including customer service inquiries, appointment scheduling, and providing information about products and services.",
    },
    {
      id: "03",
      question: "Can the bot be customized for my business needs?",
      answer:
        "Yes, the AI voice bot can be fully customized to meet your specific business requirements and integrate with your existing systems.",
    },
    {
      id: "04",
      question: "How long does it take to set up the voice bot?",
      answer:
        "Setup time varies depending on your requirements, but typically takes 2-4 weeks including customization and training.",
    },
    {
      id: "05",
      question: "How do I integrate the AI voice bot with my system?",
      answer:
        "We provide comprehensive documentation and support for integrating the AI voice bot with your existing systems through our API.",
    },
  ];

  return (
    <section id="faq" className="container mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <div className="inline-block">
          <span className="bg-[#E85C2C] text-white px-4 py-1 rounded-full text-sm font-medium">
            FAQ&apos;S
          </span>
        </div>
        <h2 className="text-3xl font-bold mt-4">Frequently Ask Questions</h2>
      </div>
      <Accordion
        type="single"
        collapsible
        className="space-y-4"
        value={activeItem}
        onValueChange={setActiveItem}
      >
        {faqs.map((faq) => (
          <AccordionItem
            key={faq.id}
            value={faq.id}
            className={`rounded-lg border transition-all duration-300 ${
              activeItem === faq.id
                ? "bg-gradient-to-r from-[#4A1E1C] to-[#E85C2C] text-white"
                : "bg-white hover:bg-gray-50"
            }`}
          >
            <AccordionTrigger className="px-6 py-4 hover:no-underline group">
              <div className="flex items-center gap-4">
                <span
                  className={`flex items-center justify-center w-10 h-10 rounded-full ${
                    activeItem === faq.id ? "bg-white/10" : ""
                  } text-lg font-medium transition-colors`}
                >
                  {faq.id}
                </span>
                <span className="text-lg font-medium">{faq.question}</span>
              </div>
            </AccordionTrigger>
            <AccordionContent
              className={`px-6 pb-4 pt-2 ${
                activeItem === faq.id ? "text-white/90" : "text-gray-600"
              }`}
            >
              {faq.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  );
}
