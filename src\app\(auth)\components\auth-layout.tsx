import React from "react";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import signinbg from "../../../../public/images/signin-bg.png";
import logo from "../../../../public/images/verbal.webp";

interface AuthLayoutProps {
  children: React.ReactNode;
  heading: string;
  subheading: string;
  className?: string;
}

const AuthLayout = ({ children, heading, className }: AuthLayoutProps) => {
  return (
    <div className="min-h-screen w-full flex flex-col lg:flex-row">
      {/* Left Section - Auth Form */}
      <div className="w-full lg:w-1/2 flex flex-col p-4 sm:p-6 lg:p-8">
        <div className="max-w-md mx-auto w-full">
          {/* Logo */}
          <div className="mb-6 sm:mb-8">
            <Link href="/" className="flex items-center gap-2">
              <Image
                src={logo}
                alt="Logo"
                width={100}
                height={100}
                className=""
              />
            </Link>
          </div>

          {/* Auth Form Section */}
          <div className="flex-1 flex items-center justify-center py-4 sm:py-8">
            <div className="w-full">
              <h1 className="text-xl sm:text-2xl font-semibold mb-2">
                {heading}
              </h1>
              <div className={cn("mt-6 sm:mt-8 w-full", className)}>
                {/* Form Content */}
                {children}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Section - Marketing Content */}
      <div className="hidden lg:block lg:w-[45%] p-8 relative overflow-hidden">
        {/* Background Image */}
        <Image
          src={signinbg}
          alt="AI Voice Solutions"
          width={800}
          height={783}
          className="absolute top-12 left-20 h-[90%] w-[90%]"
        />

        {/* Content Overlay */}
        <div className="relative h-full py-12 xl:px-12 md:px-4 flex flex-col mt-16">
          <div className="md:px-0 xl:ml-10 md:ml-12 mx-auto text-white">
            <h2 className="text-4xl mb-4">
              Empowering Businesses with Smarter{" "}
              <span className="text-4xl font-bold">Voice Solutions</span>
            </h2>
            <p className="text-lg opacity-90">
              Say goodbye to long wait times and hello to instant solutions. Our
              AI voice chatbot is always available, delivering quick, accurate,
              and human-like responses to your customers&apos; needs—day or
              night.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
