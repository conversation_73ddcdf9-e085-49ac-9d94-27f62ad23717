import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { useAuthStore } from "@/store/features/useAuthStore";

// Types
interface ScrapeResponse {
  message: string;
  scrapping_id: string;
  count?: number;
}

interface TrainingResponse {
  message: string;
  status: string;
  training_id?: string;
}

interface FileTrainingResponse {
  message: string;
  status: string;
  training_id?: string;
}

interface ScrapeRequest {
  urls: string[];
}

interface TrainingRequest {
  data: string[];
  scrappingId: string;
}

interface TextTrainingRequest {
  name: string;
  data: string;
}

interface RootState {
  auth?: {
    token: string | null;
  };
}

interface ScrapeEventData {
  status?: string;
  progress?: number;
  message?: string;
  url?: string;
  count?: number;
  scrapping_id?: string;
  [key: string]: unknown;
}

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Function to get token from Zustand store
const getZustandToken = (): string | null => {
  return useAuthStore.getState().token;
};

export const trainingApi = createApi({
  reducerPath: "trainingApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: async (headers, { getState, endpoint }) => {
      let token = getZustandToken();
  
      if (!token) {
        token = (getState() as RootState).auth?.token || null;
      }
  
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
  
      // Only set Content-Type header for non-file upload requests
      // For file uploads (multipart/form-data), let the browser set it automatically
      if (!endpoint.includes("upload") && !endpoint.includes("Audio")) {
        headers.set("Content-Type", "application/json");
      }
  
      return headers;
    },
  }),
  endpoints: (builder) => ({
    // Scrape URLs
    scrapeUrls: builder.mutation<ScrapeResponse, ScrapeRequest>({
      query: (data) => ({
        url: "/scraping/scrape",
        method: "POST",
        body: {
          urls: data.urls,
        },
      }),
    }),

    // Stream scraping status (This will be handled differently in the component)
    getScrapeStatus: builder.query<
      { status?: string; progress?: number; message?: string },
      string
    >({
      query: (url) => ({
        url: `/scraping?url=${encodeURIComponent(url)}`,
        responseHandler: "text",
        headers: {
          Accept: "text/event-stream",
        },
      }),
      // This is a placeholder - we'll handle streaming separately in the component
      transformResponse: () => ({}),
    }),

    // Initialize training with scraped URLs
    initTraining: builder.mutation<
      TrainingResponse,
      { voiceAgentId: string; data: TrainingRequest }
    >({
      query: ({ voiceAgentId, data }) => ({
        url: `/${voiceAgentId}/training/website`,
        method: "POST",
        body: {
          data: data.data,
          scrappingId: data.scrappingId,
        },
      }),
    }),

    // Get training status
    getTrainingStatus: builder.query<
      { status: string; progress?: number; message?: string },
      string
    >({
      query: (trainingId) => `/training/${trainingId}/status`,
    }),

    // New endpoint for file training
    uploadTrainingFiles: builder.mutation<
      FileTrainingResponse,
      { voiceAgentId: string; files: File[] }
    >({
      query: ({ voiceAgentId, files }) => {
        const formData = new FormData();

        // Append each file to the form data
        files.forEach((file) => {
          formData.append("files", file);
        });

        return {
          url: `/${voiceAgentId}/training/file?type=file`,
          method: "POST",
          body: formData,
        };
      },
    }),

    // New endpoint for text training
    initTextTraining: builder.mutation<
      TrainingResponse,
      { voiceAgentId: string; data: TextTrainingRequest }
    >({
      query: ({ voiceAgentId, data }) => ({
        url: `/${voiceAgentId}/training/text`,
        method: "POST",
        body: {
          name: data.name,
          data: data.data,
        },
      }),
    }),
    uploadAudioFiles: builder.mutation<
    { message: string; status: string; transcription?: string },
    { voiceAgentId: string; files: File[]; transcriptionService: string }
  >({
    query: ({ voiceAgentId, files, transcriptionService }) => {
      const formData = new FormData();
  
      // Append each file to the form data
      files.forEach((file) => {
        formData.append("files", file);
      });
  
      return {
        url: `/${voiceAgentId}/training/audio?transcription_service=${transcriptionService}`,
        method: "POST",
        // Important: Don't set the Content-Type header manually
        // Let the browser set it correctly with the boundary
        body: formData,
       
      };
      
    },
    transformResponse: (response: string) => {
      // If the response is an array with one item, return the first item
      if (Array.isArray(response) && response.length > 0) {
        return response[0];
      }
      // Otherwise return the response as is
      return response;
    },
  }),
  
  }),
});

export const {
  useScrapeUrlsMutation,
  useInitTrainingMutation,
  useGetTrainingStatusQuery,
  useUploadTrainingFilesMutation,
  useInitTextTrainingMutation,
  useUploadAudioFilesMutation,
} = trainingApi;

// This utility function will be needed for handling streaming events
export const streamScrapeStatus = async (
  url: string,
  onEvent: (event: string, data: ScrapeEventData) => void,
  onComplete: () => void,
  onError: (error: Error) => void
) => {
  try {
    const token = getZustandToken();

    if (!token) {
      throw new Error("No authentication token found");
    }

    const response = await fetch(
      `${BASE_URL}/scraping?url=${encodeURIComponent(url)}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "text/event-stream",
        },
      }
    );

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Failed to read response");
    }

    let buffer = "";
    const textDecoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        onComplete();
        break;
      }

      buffer += textDecoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        if (trimmedLine.startsWith("event: ")) {
          const event = trimmedLine.substring(7).trim();
          const nextLine = lines.find((l) => l.trim().startsWith("data: "));

          if (nextLine) {
            try {
              const data = JSON.parse(nextLine.trim().substring(6));
              onEvent(event, data);
            } catch (e) {
              console.error("Error parsing data:", e);
            }
          }
        }
      }
    }
  } catch (error) {
    onError(error instanceof Error ? error : new Error("Failed to fetch data"));
  }
};
